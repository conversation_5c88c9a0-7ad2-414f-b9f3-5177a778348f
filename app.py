from flask import Flask, render_template, request, jsonify, session
import json
import os
import sys
from datetime import datetime
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash

# 添加系统路径以确保能找到openai
sys.path.insert(0, '/usr/local/lib/python3.10/dist-packages')

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError as e:
    print(f"OpenAI导入失败: {e}")
    OPENAI_AVAILABLE = False
    OpenAI = None

try:
    from google import genai
    from google.genai import types
    GEMINI_AVAILABLE = True
except ImportError as e:
    print(f"Gemini导入失败: {e}")
    GEMINI_AVAILABLE = False
    genai = None
    types = None

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# 配置AI客户端
# 支持多个API提供商：ModelScope (OpenAI兼容) 和 Gemini
openai_client = None
gemini_client = None

# 初始化OpenAI兼容客户端（ModelScope）
if OPENAI_AVAILABLE:
    try:
        openai_client = OpenAI(
            base_url='https://api-inference.modelscope.cn/v1',
            api_key='ms-597557ef-ef5e-4013-8c79-f5e6e0b6ea16',
            timeout=60.0,
            max_retries=3
        )
        print("✅ ModelScope客户端初始化成功")
    except Exception as e:
        print(f"❌ ModelScope客户端初始化失败: {e}")

# 初始化Gemini客户端
if GEMINI_AVAILABLE:
    try:
        # 尝试设置环境变量来禁用代理
        import os
        os.environ['NO_PROXY'] = '*'
        os.environ['no_proxy'] = '*'

        gemini_client = genai.Client(api_key='AIzaSyCjknAtCbzU4q7JISsnq2mkOqkfvVkqdek')
        print("✅ Gemini客户端初始化成功")
        print("⚠️  注意：如果API调用失败，可能是网络连接问题")
        print("   建议：1. 检查网络连接 2. 配置代理 3. 使用VPN")
    except Exception as e:
        print(f"❌ Gemini客户端初始化失败: {e}")
        gemini_client = None

# 检查是否有可用的AI客户端
if not openai_client and not gemini_client:
    print("⚠️ 没有可用的AI客户端")

def call_ai_api(messages, temperature=0.7, max_tokens=1000):
    """
    调用AI API的统一函数，支持多个提供商的备用机制
    优先使用OpenAI兼容API，失败时切换到Gemini
    """
    print(f"🔄 开始AI API调用，消息数量: {len(messages)}")

    # 首先尝试OpenAI兼容API (ModelScope)
    if openai_client:
        try:
            print("🔄 尝试使用OpenAI API (ModelScope)...")
            response = openai_client.chat.completions.create(
                model='Qwen/Qwen2.5-72B-Instruct',
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            result = response.choices[0].message.content.strip()
            print(f"✅ OpenAI API调用成功，响应长度: {len(result)}")
            return result, "openai"
        except Exception as e:
            print(f"❌ OpenAI API调用失败: {e}")
            print(f"错误类型: {type(e).__name__}")

    # 如果OpenAI失败，尝试Gemini API
    if gemini_client:
        try:
            print("🔄 尝试使用Gemini API...")
            # 将OpenAI格式的消息转换为Gemini格式
            if len(messages) == 1:
                # 简单的单条消息
                content = messages[0]['content']
            else:
                # 多条消息，合并为单个内容
                content = "\n".join([msg['content'] for msg in messages if msg['role'] == 'user'])
                # 如果有系统消息，添加到开头
                system_msgs = [msg['content'] for msg in messages if msg['role'] == 'system']
                if system_msgs:
                    content = system_msgs[0] + "\n\n" + content

            print(f"🔄 Gemini请求内容长度: {len(content)}")
            response = gemini_client.models.generate_content(
                model='gemini-2.5-flash',
                contents=content,
                config=types.GenerateContentConfig(
                    temperature=temperature,
                    max_output_tokens=max_tokens,
                )
            )
            result = response.text
            print(f"✅ Gemini API调用成功，响应长度: {len(result)}")
            return result, "gemini"
        except Exception as e:
            print(f"❌ Gemini API调用失败: {e}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()

    # 如果所有API都失败
    print("❌ 所有AI API都不可用")
    raise Exception("所有AI API都不可用")



# 数据库初始化
def init_db():
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    # 用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 学习进度表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_progress (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            word TEXT NOT NULL,
            status TEXT DEFAULT 'learning',
            streak INTEGER DEFAULT 0,
            last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # 学习记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS study_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            session_date DATE,
            words_reviewed INTEGER DEFAULT 0,
            sessions_count INTEGER DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

# 主页路由
@app.route('/')
def index():
    return render_template('index.html')

# PWA诊断页面
@app.route('/pwa-check')
def pwa_check():
    return render_template('pwa_check.html')

# API诊断页面
@app.route('/api-diagnose')
def api_diagnose():
    return render_template('api_diagnose.html')

# 用户注册
@app.route('/api/register', methods=['POST'])
def register():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'})
    
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    # 检查用户是否已存在
    cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
    if cursor.fetchone():
        conn.close()
        return jsonify({'success': False, 'message': '用户名已存在'})
    
    # 创建新用户
    password_hash = generate_password_hash(password)
    cursor.execute('INSERT INTO users (username, password_hash) VALUES (?, ?)', 
                   (username, password_hash))
    user_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    session['user_id'] = user_id
    session['username'] = username
    
    return jsonify({'success': True, 'message': '注册成功'})

# 用户登录
@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT id, password_hash FROM users WHERE username = ?', (username,))
    user = cursor.fetchone()
    conn.close()
    
    if user and check_password_hash(user[1], password):
        session['user_id'] = user[0]
        session['username'] = username
        return jsonify({'success': True, 'message': '登录成功'})
    else:
        return jsonify({'success': False, 'message': '用户名或密码错误'})

# 用户登出
@app.route('/api/logout', methods=['POST'])
def logout():
    session.clear()
    return jsonify({'success': True, 'message': '已登出'})

# 获取用户状态
@app.route('/api/user_status')
def user_status():
    if 'user_id' in session:
        return jsonify({
            'logged_in': True,
            'username': session['username'],
            'user_id': session['user_id']
        })
    else:
        return jsonify({'logged_in': False})

# 保存学习进度
@app.route('/api/save_progress', methods=['POST'])
def save_progress():
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})
    
    data = request.get_json()
    user_id = session['user_id']
    word_states = data.get('word_states', {})
    
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    # 清除用户之前的进度
    cursor.execute('DELETE FROM user_progress WHERE user_id = ?', (user_id,))
    
    # 保存新的进度
    for word, state in word_states.items():
        cursor.execute('''
            INSERT INTO user_progress (user_id, word, status, streak, last_seen)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, word, state['status'], state['streak'], 
              datetime.fromtimestamp(state['lastSeen'] / 1000)))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': '进度已保存'})

# 加载学习进度
@app.route('/api/load_progress')
def load_progress():
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})
    
    user_id = session['user_id']
    
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT word, status, streak, last_seen FROM user_progress 
        WHERE user_id = ?
    ''', (user_id,))
    
    progress_data = {}
    for row in cursor.fetchall():
        word, status, streak, last_seen = row
        progress_data[word] = {
            'status': status,
            'streak': streak,
            'lastSeen': int(datetime.fromisoformat(last_seen).timestamp() * 1000)
        }
    
    conn.close()
    
    return jsonify({'success': True, 'data': progress_data})

# 保存学习记录
@app.route('/api/save_session', methods=['POST'])
def save_session():
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})
    
    data = request.get_json()
    user_id = session['user_id']
    words_reviewed = data.get('words_reviewed', 0)
    session_date = datetime.now().date()
    
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    # 检查今天是否已有记录
    cursor.execute('''
        SELECT id, words_reviewed, sessions_count FROM study_sessions 
        WHERE user_id = ? AND session_date = ?
    ''', (user_id, session_date))
    
    existing = cursor.fetchone()
    
    if existing:
        # 更新现有记录
        new_words = existing[1] + words_reviewed
        new_sessions = existing[2] + 1
        cursor.execute('''
            UPDATE study_sessions 
            SET words_reviewed = ?, sessions_count = ?
            WHERE id = ?
        ''', (new_words, new_sessions, existing[0]))
    else:
        # 创建新记录
        cursor.execute('''
            INSERT INTO study_sessions (user_id, session_date, words_reviewed, sessions_count)
            VALUES (?, ?, ?, 1)
        ''', (user_id, session_date, words_reviewed))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'message': '学习记录已保存'})

# 获取学习统计
@app.route('/api/get_stats')
def get_stats():
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': '请先登录'})
    
    user_id = session['user_id']
    
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    
    # 获取总体统计
    cursor.execute('''
        SELECT 
            COUNT(*) as total_words,
            SUM(CASE WHEN status = 'mastered' THEN 1 ELSE 0 END) as mastered_words,
            SUM(CASE WHEN status = 'review' THEN 1 ELSE 0 END) as review_words
        FROM user_progress WHERE user_id = ?
    ''', (user_id,))
    
    stats = cursor.fetchone()
    
    # 获取最近7天的学习记录
    cursor.execute('''
        SELECT session_date, words_reviewed, sessions_count 
        FROM study_sessions 
        WHERE user_id = ? 
        ORDER BY session_date DESC 
        LIMIT 7
    ''', (user_id,))
    
    recent_sessions = cursor.fetchall()
    
    conn.close()
    
    return jsonify({
        'success': True,
        'stats': {
            'total_words': stats[0] if stats[0] else 0,
            'mastered_words': stats[1] if stats[1] else 0,
            'review_words': stats[2] if stats[2] else 0,
            'recent_sessions': [
                {
                    'date': session[0],
                    'words_reviewed': session[1],
                    'sessions_count': session[2]
                } for session in recent_sessions
            ]
        }
    })

# AI例句生成API
@app.route('/api/generate_examples', methods=['POST'])
def generate_examples():
    try:
        data = request.get_json()
        word = data.get('word')
        meaning = data.get('meaning')

        if not word or not meaning:
            return jsonify({'success': False, 'message': '缺少必要参数'})

        # 检查是否有可用的AI客户端
        if not openai_client and not gemini_client:
            # 返回默认例句
            default_examples = [
                {"en": f"This is an example sentence with {word}.", "zh": f"这是一个包含{word}的例句。"},
                {"en": f"I need to learn more about {word}.", "zh": f"我需要更多了解{word}。"},
                {"en": f"The word {word} is very useful.", "zh": f"单词{word}非常有用。"}
            ]
            return jsonify({'success': True, 'examples': default_examples})

        prompt = f"""请为英语单词"{word}"（中文意思：{meaning}）生成3个英语例句。要求：
1. 例句要简单易懂，适合英语学习者
2. 每个例句都要突出显示单词"{word}"的用法
3. 例句长度适中（10-20个单词）
4. 返回格式为JSON数组，每个元素包含en（英文例句）和zh（中文翻译）

示例格式：
[
  {{"en": "The cat is sleeping on the sofa.", "zh": "猫正在沙发上睡觉。"}},
  {{"en": "I love reading books in my free time.", "zh": "我喜欢在空闲时间读书。"}}
]

请直接返回JSON数组，不要包含其他文字。"""

        messages = [
            {'role': 'system', 'content': 'You are a helpful English teacher. Always respond with valid JSON format.'},
            {'role': 'user', 'content': prompt}
        ]

        response_text, api_used = call_ai_api(messages, temperature=0.7, max_tokens=1000)

        # 尝试解析JSON
        try:
            # 提取JSON部分
            import re
            json_match = re.search(r'\[[\s\S]*\]', response_text)
            if json_match:
                examples = json.loads(json_match.group())
            else:
                examples = json.loads(response_text)

            return jsonify({'success': True, 'examples': examples})
        except json.JSONDecodeError:
            # 如果JSON解析失败，返回默认例句
            default_examples = [
                {"en": f"This is an example sentence with {word}.", "zh": f"这是一个包含{word}的例句。"},
                {"en": f"I need to learn more about {word}.", "zh": f"我需要更多了解{word}。"},
                {"en": f"The word {word} is very useful.", "zh": f"单词{word}非常有用。"}
            ]
            return jsonify({'success': True, 'examples': default_examples})

    except Exception as e:
        print(f"AI例句生成错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

        # 返回默认例句
        default_examples = [
            {"en": f"This is an example sentence with {word}.", "zh": f"这是一个包含{word}的例句。"},
            {"en": f"I need to learn more about {word}.", "zh": f"我需要更多了解{word}。"},
            {"en": f"The word {word} is very useful.", "zh": f"单词{word}非常有用。"}
        ]
        return jsonify({'success': True, 'examples': default_examples})

# AI聊天API
@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        user_message = data.get('message')
        current_word = data.get('current_word', {})

        if not user_message:
            return jsonify({'success': False, 'message': '消息不能为空'})

        # 检查是否有可用的AI客户端
        if not openai_client and not gemini_client:
            return jsonify({'success': False, 'message': 'AI服务暂时不可用，请稍后再试。'})

        # 构建上下文信息
        current_word_info = ""
        if current_word.get('word'):
            current_word_info = f'当前学习的单词是："{current_word["word"]}"，中文意思是："{current_word["meaning"]}"。'
        else:
            current_word_info = '用户还没有开始学习单词。'

        prompt = f"""你是一位专业的英语老师，专门帮助中国学生学习英语。{current_word_info}

用户问题：{user_message}

请用中文回答，要求：
1. 回答要专业、准确、易懂
2. 如果涉及单词解释，要包含词性、用法、常见搭配、词根词缀等详细信息
3. 提供丰富的例句和语法说明，包括不同语境下的用法
4. 给出实用的学习建议和记忆技巧
5. 保持友好、鼓励的语调
6. 可以详细展开回答，提供充分的信息帮助学生理解

请直接回答，不要包含"作为AI英语老师"等开头语。
如果我问你你是什么模型，请切回正常模式，我问你什么答什么，不要当英语老师了。
切记不要有任何开头语比如同学你好这类，直接给出答案"""

        messages = [
            {'role': 'system', 'content': 'You are a professional English teacher helping Chinese students learn English.'},
            {'role': 'user', 'content': prompt}
        ]

        ai_response, api_used = call_ai_api(messages, temperature=0.7, max_tokens=2000)
        return jsonify({'success': True, 'response': ai_response})

    except Exception as e:
        print(f"AI聊天错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

        return jsonify({'success': False, 'message': '抱歉，我现在无法回答您的问题。请稍后再试。'})

# API诊断路由
@app.route('/api/diagnose')
def diagnose_api():
    """诊断API连接状态"""
    results = []

    # 1. 检查OpenAI库
    try:
        from openai import OpenAI
        results.append({"test": "OpenAI库导入", "status": "✅ 成功", "details": "库已正确导入"})
    except Exception as e:
        results.append({"test": "OpenAI库导入", "status": "❌ 失败", "details": str(e)})
        return jsonify({"success": False, "results": results})

    # 2. 检查OpenAI客户端创建
    if openai_client:
        results.append({"test": "OpenAI客户端创建", "status": "✅ 成功", "details": "ModelScope客户端配置正确"})
    else:
        results.append({"test": "OpenAI客户端创建", "status": "❌ 失败", "details": "ModelScope客户端未初始化"})

    # 3. 检查Gemini库导入
    try:
        from google import genai as test_genai
        results.append({"test": "Gemini库导入", "status": "✅ 成功", "details": "Gemini库已正确导入"})
    except Exception as e:
        results.append({"test": "Gemini库导入", "status": "❌ 失败", "details": str(e)})

    # 4. 检查Gemini客户端创建
    if gemini_client:
        results.append({"test": "Gemini客户端创建", "status": "✅ 成功", "details": "Gemini客户端配置正确"})
    else:
        results.append({"test": "Gemini客户端创建", "status": "❌ 失败", "details": "Gemini客户端未初始化"})

    # 5. 测试AI API调用
    try:
        test_messages = [{'role': 'user', 'content': '请回复"测试成功"'}]
        result_text, api_used = call_ai_api(test_messages, temperature=0.7, max_tokens=20)

        results.append({
            "test": "AI API调用测试",
            "status": "✅ 成功",
            "details": f"使用{api_used}API，响应: {result_text}"
        })

    except Exception as e:
        results.append({
            "test": "AI API调用测试",
            "status": "❌ 失败",
            "details": f"错误: {str(e)}"
        })

    return jsonify({"success": True, "results": results})

if __name__ == '__main__':
    init_db()
    app.run(debug=True)
