import sqlite3
from datetime import datetime

print("📊 开始分析单词学习数据...")

try:
    # 连接数据库
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    print("✅ 成功连接到数据库\n")
    
    # 1. 用户统计
    print("👥 用户统计:")
    cursor.execute('SELECT COUNT(*) FROM users')
    user_count = cursor.fetchone()[0]
    print(f"  总用户数: {user_count}")
    
    cursor.execute('SELECT username, created_at FROM users ORDER BY created_at')
    users = cursor.fetchall()
    for username, created_at in users:
        print(f"  - {username} (注册时间: {created_at})")
    
    print("\n" + "="*60)
    
    # 2. 学习进度统计
    print("\n📚 学习进度统计:")
    
    # 按状态统计单词数量
    cursor.execute('''
        SELECT status, COUNT(*) as count 
        FROM user_progress 
        GROUP BY status 
        ORDER BY count DESC
    ''')
    status_stats = cursor.fetchall()
    
    print("  按学习状态分类:")
    status_names = {
        'learning': '学习中',
        'mastered': '已掌握',
        'review': '需复习'
    }
    
    for status, count in status_stats:
        status_name = status_names.get(status, status)
        print(f"    {status_name}: {count} 个单词")
    
    # 显示所有单词的详细信息
    print("\n  单词详细信息:")
    cursor.execute('''
        SELECT u.username, up.word, up.status, up.streak, up.last_seen
        FROM user_progress up
        JOIN users u ON up.user_id = u.id
        ORDER BY up.streak DESC, up.word
    ''')
    
    word_progress = cursor.fetchall()
    for username, word, status, streak, last_seen in word_progress:
        status_name = status_names.get(status, status)
        print(f"    {word:12} | {status_name:6} | 连击:{streak:2} | 用户:{username} | 最后学习:{last_seen[:10]}")
    
    print("\n" + "="*60)
    
    # 3. 学习记录统计
    print("\n📈 学习记录统计:")
    
    cursor.execute('''
        SELECT 
            COUNT(*) as total_sessions,
            SUM(words_reviewed) as total_words,
            AVG(words_reviewed) as avg_words_per_session,
            MAX(words_reviewed) as max_words,
            MIN(words_reviewed) as min_words
        FROM study_sessions
    ''')
    
    session_stats = cursor.fetchone()
    total_sessions, total_words, avg_words, max_words, min_words = session_stats
    
    print(f"  总学习会话数: {total_sessions}")
    print(f"  总复习单词数: {total_words}")
    print(f"  平均每次复习: {avg_words:.1f} 个单词")
    print(f"  单次最多复习: {max_words} 个单词")
    print(f"  单次最少复习: {min_words} 个单词")
    
    # 按日期显示学习记录
    print("\n  每日学习记录:")
    cursor.execute('''
        SELECT u.username, ss.session_date, ss.words_reviewed, ss.sessions_count
        FROM study_sessions ss
        JOIN users u ON ss.user_id = u.id
        ORDER BY ss.session_date DESC
    ''')
    
    daily_records = cursor.fetchall()
    for username, session_date, words_reviewed, sessions_count in daily_records:
        print(f"    {session_date} | {username:10} | 复习 {words_reviewed:2} 个单词 | {sessions_count} 次会话")
    
    print("\n" + "="*60)
    
    # 4. 高级统计
    print("\n🏆 高级统计:")
    
    # 连击最高的单词
    cursor.execute('''
        SELECT word, streak, status 
        FROM user_progress 
        ORDER BY streak DESC 
        LIMIT 3
    ''')
    top_streaks = cursor.fetchall()
    print("  连击最高的单词:")
    for word, streak, status in top_streaks:
        status_name = status_names.get(status, status)
        print(f"    {word}: {streak} 连击 ({status_name})")
    
    # 最活跃的学习日
    cursor.execute('''
        SELECT session_date, SUM(words_reviewed) as total_words
        FROM study_sessions 
        GROUP BY session_date 
        ORDER BY total_words DESC 
        LIMIT 3
    ''')
    top_days = cursor.fetchall()
    print("\n  最活跃的学习日:")
    for session_date, total_words in top_days:
        print(f"    {session_date}: 复习了 {total_words} 个单词")
    
    # 学习趋势
    cursor.execute('''
        SELECT session_date, words_reviewed
        FROM study_sessions 
        ORDER BY session_date
    ''')
    trend_data = cursor.fetchall()
    
    if len(trend_data) >= 2:
        first_day_words = trend_data[0][1]
        last_day_words = trend_data[-1][1]
        trend = "📈 上升" if last_day_words > first_day_words else "📉 下降" if last_day_words < first_day_words else "➡️ 平稳"
        print(f"\n  学习趋势: {trend}")
        print(f"    首日复习: {first_day_words} 个单词")
        print(f"    最近复习: {last_day_words} 个单词")
    
    conn.close()
    print(f"\n🎉 数据分析完成!")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("\n脚本执行结束")
