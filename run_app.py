#!/usr/bin/env python3
"""
启动单词学习应用的脚本
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """检查必要的依赖"""
    print("🔍 检查依赖...")
    
    required_packages = ['flask', 'werkzeug', 'sqlite3']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖: {', '.join(missing_packages)}")
        print("请运行: pip install flask werkzeug")
        return False
    
    return True

def check_database():
    """检查数据库文件"""
    print("\n🗄️  检查数据库...")
    
    if os.path.exists('flashcard.db'):
        print("  ✅ 数据库文件存在")
        
        # 检查数据库内容
        import sqlite3
        try:
            conn = sqlite3.connect('flashcard.db')
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if tables:
                print(f"  ✅ 数据库包含 {len(tables)} 个表")
                
                # 检查是否有数据
                cursor.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM user_progress")
                progress_count = cursor.fetchone()[0]
                
                print(f"  📊 用户数: {user_count}, 学习记录: {progress_count}")
                
            else:
                print("  ⚠️  数据库为空，将自动初始化")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 数据库检查失败: {e}")
            return False
    else:
        print("  ⚠️  数据库文件不存在，将自动创建")
    
    return True

def start_app():
    """启动Flask应用"""
    print("\n🚀 启动应用...")
    
    try:
        # 设置环境变量
        os.environ['FLASK_APP'] = 'app.py'
        os.environ['FLASK_ENV'] = 'development'
        
        print("  📱 应用将在以下地址启动:")
        print("     本地访问: http://127.0.0.1:5000")
        print("     局域网访问: http://0.0.0.0:5000")
        print("\n  💡 提示:")
        print("     - 按 Ctrl+C 停止应用")
        print("     - 可以使用测试账号登录:")
        print("       用户名: demo_user, 密码: demo123")
        print("       用户名: testuser1, 密码: password123")
        
        print("\n" + "="*50)
        print("🎯 应用启动中...")
        print("="*50)
        
        # 启动Flask应用
        from app import app, init_db
        
        # 初始化数据库
        init_db()
        
        # 启动应用
        app.run(
            host='0.0.0.0',  # 允许外部访问
            port=5000,
            debug=True,
            use_reloader=False  # 避免重复启动
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎓 单词学习应用启动器")
    print("="*40)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查app.py文件
    if not os.path.exists('app.py'):
        print("❌ 找不到 app.py 文件")
        print("请确保在正确的目录中运行此脚本")
        return
    
    print("✅ 找到 app.py 文件")
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 检查数据库
    if not check_database():
        return
    
    # 启动应用
    start_app()

if __name__ == '__main__':
    main()
