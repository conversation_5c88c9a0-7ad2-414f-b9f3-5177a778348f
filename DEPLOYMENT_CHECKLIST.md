# 🚀 部署检查清单

## 📋 部署前准备

### 1. 清理文件
```bash
python cleanup_files.py
```

### 2. 核心文件确认
确保以下文件存在：
- [ ] `app.py` - Flask主应用
- [ ] `wsgi.py` - WSGI配置文件
- [ ] `requirements.txt` - Python依赖
- [ ] `README.md` - 说明文档
- [ ] `templates/index.html` - 主页面模板
- [ ] `static/script.js` - JavaScript逻辑
- [ ] `static/styles.css` - CSS样式
- [ ] `static/vocab_data.json` - 词汇数据
- [ ] `static/manifest.json` - PWA配置
- [ ] `static/sw.js` - Service Worker
- [ ] `static/icons/` - 图标文件夹（包含所有尺寸图标）

## 🌐 PythonAnywhere部署步骤

### 1. 上传文件
- [ ] 将所有核心文件上传到 `/home/<USER>/demo/`
- [ ] 确保文件夹结构正确

### 2. 安装依赖
```bash
cd ~/demo
pip3.10 install --user -r requirements.txt
```

### 3. 修改配置
编辑 `wsgi.py`，将用户名替换为实际用户名：
```python
path = '/home/<USER>/demo'  # 替换yourusername
```

### 4. Web应用配置
在PythonAnywhere Web标签页：
- [ ] **Source code**: `/home/<USER>/demo`
- [ ] **Working directory**: `/home/<USER>/demo`
- [ ] **WSGI configuration file**: `/home/<USER>/demo/wsgi.py`

### 5. 静态文件配置
添加静态文件映射：
- [ ] **URL**: `/static/`
- [ ] **Directory**: `/home/<USER>/demo/static/`

### 6. 重新加载应用
- [ ] 点击绿色"Reload"按钮

## 📱 功能测试清单

### 基础功能测试
- [ ] 网站可以正常访问
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 单词学习功能正常
- [ ] AI例句生成正常
- [ ] 语音发音功能正常

### PWA功能测试
- [ ] 访问 `/pwa-check` 页面检查PWA状态
- [ ] Service Worker注册成功
- [ ] Manifest文件可访问
- [ ] 图标文件加载正常
- [ ] 离线功能测试

### App下载功能测试
- [ ] App下载区域显示正常
- [ ] "立即安装"按钮功能正常
- [ ] "安装指导"按钮显示指导信息
- [ ] 不同浏览器显示对应的安装指导

### 移动端测试
- [ ] 手机浏览器访问正常
- [ ] 响应式设计适配良好
- [ ] PWA安装提示正常弹出
- [ ] 安装后可以离线使用

## 🔧 常见问题解决

### 问题1: 500错误
- 检查wsgi.py中的路径是否正确
- 检查Python依赖是否安装成功
- 查看错误日志

### 问题2: 静态文件无法加载
- 检查静态文件路径配置
- 确认文件上传完整
- 检查文件权限

### 问题3: PWA安装提示不出现
- 确认HTTPS正常工作
- 检查Service Worker注册
- 在网站上停留更长时间
- 尝试不同浏览器

### 问题4: 数据库错误
- 确认应用有写入权限
- 数据库文件会自动创建
- 检查SQLite支持

## 📊 性能优化建议

### 1. 缓存优化
- Service Worker已配置缓存
- 静态资源自动缓存
- API请求合理缓存

### 2. 加载优化
- 图标文件已压缩
- CSS/JS文件已优化
- 字体文件CDN加载

### 3. 移动端优化
- 响应式设计
- 触摸友好界面
- 快速加载体验

## 🎯 部署后验证

### 最终检查清单
- [ ] 网站在桌面端正常工作
- [ ] 网站在移动端正常工作
- [ ] PWA可以成功安装
- [ ] 用户系统功能完整
- [ ] 学习功能运行正常
- [ ] 离线功能可用

### 用户体验测试
- [ ] 新用户注册流程顺畅
- [ ] 学习体验良好
- [ ] App安装过程简单
- [ ] 界面美观易用

## 🎉 部署完成

恭喜！您的英语闪卡学习系统已经成功部署！

### 分享给用户
- 网站地址：`https://yourusername.pythonanywhere.com`
- 建议用户使用Chrome或Safari浏览器
- 引导用户安装PWA获得更好体验

### 后续维护
- 定期检查应用运行状态
- 根据用户反馈优化功能
- 更新词汇数据和AI功能
