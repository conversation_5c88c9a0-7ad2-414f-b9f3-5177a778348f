<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .check-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .check-item.success {
            border-left-color: #4CAF50;
        }
        .check-item.error {
            border-left-color: #f44336;
        }
        .check-item.warning {
            border-left-color: #ff9800;
        }
        .status {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .install-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        .install-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>PWA状态检查</h1>
    <p>这个页面帮助您诊断PWA安装问题</p>
    
    <div id="checks"></div>
    
    <button id="manualInstall" class="install-btn" style="display: none;">
        手动触发安装提示
    </button>
    
    <button onclick="location.href='/'" class="install-btn">
        返回主页
    </button>

    <script>
        const checksContainer = document.getElementById('checks');
        const manualInstallBtn = document.getElementById('manualInstall');
        let deferredPrompt;

        function addCheck(title, status, message, type = 'success') {
            const div = document.createElement('div');
            div.className = `check-item ${type}`;
            div.innerHTML = `
                <div class="status">${status} ${title}</div>
                <div>${message}</div>
            `;
            checksContainer.appendChild(div);
        }

        // 检查HTTPS
        if (location.protocol === 'https:') {
            addCheck('HTTPS支持', '✅', 'PWA需要HTTPS，当前已满足');
        } else {
            addCheck('HTTPS支持', '❌', 'PWA需要HTTPS，当前使用HTTP', 'error');
        }

        // 检查Service Worker支持
        if ('serviceWorker' in navigator) {
            addCheck('Service Worker支持', '✅', '浏览器支持Service Worker');
            
            // 检查Service Worker注册状态
            navigator.serviceWorker.getRegistrations().then(registrations => {
                if (registrations.length > 0) {
                    addCheck('Service Worker注册', '✅', `已注册${registrations.length}个Service Worker`);
                } else {
                    addCheck('Service Worker注册', '⚠️', 'Service Worker未注册，正在尝试注册...', 'warning');
                    
                    // 尝试注册
                    navigator.serviceWorker.register('/static/sw.js')
                        .then(registration => {
                            addCheck('Service Worker注册', '✅', 'Service Worker注册成功');
                        })
                        .catch(error => {
                            addCheck('Service Worker注册', '❌', `注册失败: ${error.message}`, 'error');
                        });
                }
            });
        } else {
            addCheck('Service Worker支持', '❌', '浏览器不支持Service Worker', 'error');
        }

        // 检查Manifest
        fetch('/static/manifest.json')
            .then(response => {
                if (response.ok) {
                    addCheck('Manifest文件', '✅', 'manifest.json文件可访问');
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(manifest => {
                addCheck('Manifest内容', '✅', `应用名称: ${manifest.name || manifest.short_name}`);
            })
            .catch(error => {
                addCheck('Manifest文件', '❌', `无法加载manifest.json: ${error.message}`, 'error');
            });

        // 检查安装提示支持
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            addCheck('安装提示', '✅', '浏览器支持安装提示');
            manualInstallBtn.style.display = 'block';
        });

        // 手动触发安装
        manualInstallBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                addCheck('安装结果', outcome === 'accepted' ? '✅' : '⚠️', 
                        `用户${outcome === 'accepted' ? '接受' : '拒绝'}了安装`, 
                        outcome === 'accepted' ? 'success' : 'warning');
                deferredPrompt = null;
                manualInstallBtn.style.display = 'none';
            }
        });

        // 检查是否已安装
        if (window.matchMedia('(display-mode: standalone)').matches) {
            addCheck('PWA状态', '✅', '当前以PWA模式运行');
        } else {
            addCheck('PWA状态', '⚠️', '当前以浏览器模式运行', 'warning');
        }

        // 检查浏览器类型
        const userAgent = navigator.userAgent;
        let browserInfo = '';
        if (userAgent.includes('Chrome')) {
            browserInfo = 'Chrome - 完全支持PWA';
        } else if (userAgent.includes('Safari')) {
            browserInfo = 'Safari - 部分支持PWA（iOS 11.3+）';
        } else if (userAgent.includes('Firefox')) {
            browserInfo = 'Firefox - 支持PWA（需手动添加）';
        } else {
            browserInfo = '未知浏览器 - 可能不完全支持PWA';
        }
        addCheck('浏览器兼容性', '📱', browserInfo, 'warning');

        // 延迟检查安装提示
        setTimeout(() => {
            if (!deferredPrompt) {
                addCheck('安装提示', '⚠️', '未触发安装提示，可能需要：1) 多次访问网站 2) 在网站上停留更长时间 3) 与网站进行更多交互', 'warning');
            }
        }, 3000);
    </script>
</body>
</html>
