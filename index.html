<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语闪卡学习系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                <h1>英语闪卡</h1>
            </div>
            <div class="user-section" id="userSection">
                <!-- 未登录状态 -->
                <div class="auth-buttons" id="authButtons">
                    <button class="auth-btn" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </button>
                    <button class="auth-btn" id="registerBtn">
                        <i class="fas fa-user-plus"></i>
                        注册
                    </button>
                </div>
                <!-- 已登录状态 -->
                <div class="user-info" id="userInfo" style="display: none;">
                    <span class="welcome-text">欢迎, <span id="usernameDisplay"></span></span>
                    <button class="auth-btn" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        登出
                    </button>
                </div>
            </div>
            <div class="stats">
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>学习中: <span id="learningCount">0</span></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-redo"></i>
                    <span>需复习: <span id="reviewCount">0</span></span>
                </div>
                <button class="reset-btn" id="resetProgressBtn" title="重置所有学习进度">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
        </header>

        <!-- 模式选择 -->
        <div class="mode-selector" id="modeSelector">
            <h2>选择学习模式</h2>
            <div class="mode-buttons">
                <button class="mode-btn" id="normalModeBtn">
                    <i class="fas fa-play"></i>
                    <span>正常学习</span>
                    <small>学习新单词</small>
                </button>
                <button class="mode-btn" id="reviewModeBtn">
                    <i class="fas fa-redo"></i>
                    <span>复习模式</span>
                    <small>复习错误单词</small>
                </button>
            </div>

            <!-- 帮助提示 -->
            <div class="help-tips">
                <h3><i class="fas fa-keyboard"></i> 快捷键提示</h3>
                <div class="tips-grid">
                    <div class="tip">
                        <kbd>1/A</kbd> <span>选择选项A</span>
                    </div>
                    <div class="tip">
                        <kbd>2/B</kbd> <span>选择选项B</span>
                    </div>
                    <div class="tip">
                        <kbd>3/C</kbd> <span>选择选项C</span>
                    </div>
                    <div class="tip">
                        <kbd>4/D</kbd> <span>选择选项D</span>
                    </div>
                    <div class="tip">
                        <kbd>空格</kbd> <span>发音</span>
                    </div>
                    <div class="tip">
                        <kbd>回车</kbd> <span>下一个</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习界面 -->
        <div class="study-container" id="studyContainer" style="display: none;">
            <!-- 进度条 -->
            <div class="progress-container">
                <div class="progress-info">
                    <div class="progress-text">
                        <span>进度: <span id="currentWord">1</span> / <span id="totalWords">2220</span></span>
                    </div>
                    <div class="progress-percentage" id="progressPercentage">0%</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>

            <!-- 单词卡片 -->
            <div class="word-card" id="wordCard">
                <div class="word-header">
                    <div class="mode-indicator" id="modeIndicator">
                        <i class="fas fa-play"></i>
                        <span>正常模式</span>
                    </div>
                </div>
                
                <div class="word-display">
                    <div class="flip-card" id="flipCard">
                        <div class="flip-card-inner">
                            <!-- 正面：单词和发音 -->
                            <div class="flip-card-front">
                                <h2 class="english-word" id="englishWord">Loading...</h2>
                                <div class="word-pronunciation" id="pronunciation">
                                    <button class="pronounce-btn" id="pronounceBtn">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                </div>
                                <div class="flip-hint" id="flipHint" style="display: none;">
                                    <i class="fas fa-hand-pointer"></i>
                                    <span>点击单词查看例句</span>
                                </div>
                            </div>
                            <!-- 背面：例句 -->
                            <div class="flip-card-back">
                                <div class="examples-container" id="examplesContainer">
                                    <h3>📝 AI生成例句</h3>
                                    <div class="examples-list" id="examplesList">
                                        <!-- 例句将通过AI实时生成 -->
                                    </div>
                                    <div class="flip-back-hint">
                                        <i class="fas fa-undo"></i>
                                        <span>点击返回</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="options-container" id="optionsContainer">
                    <div class="option" data-option="0">
                        <span class="option-letter">A</span>
                        <span class="option-text" id="option0">选项A</span>
                    </div>
                    <div class="option" data-option="1">
                        <span class="option-letter">B</span>
                        <span class="option-text" id="option1">选项B</span>
                    </div>
                    <div class="option" data-option="2">
                        <span class="option-letter">C</span>
                        <span class="option-text" id="option2">选项C</span>
                    </div>
                    <div class="option" data-option="3">
                        <span class="option-letter">D</span>
                        <span class="option-text" id="option3">选项D</span>
                    </div>
                </div>

                <div class="feedback" id="feedback" style="display: none;">
                    <div class="feedback-content">
                        <i class="feedback-icon"></i>
                        <span class="feedback-text"></span>
                    </div>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="controls">
                <button class="control-btn secondary" id="backBtn">
                    <i class="fas fa-arrow-left"></i>
                    返回
                </button>
                <button class="control-btn primary" id="nextBtn" style="display: none;">
                    <i class="fas fa-arrow-right"></i>
                    下一个
                </button>
            </div>
        </div>

        <!-- 完成界面 -->
        <div class="completion-screen" id="completionScreen" style="display: none;">
            <div class="completion-content">
                <div class="completion-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h2>恭喜完成！</h2>
                <div class="completion-stats">
                    <div class="completion-stat">
                        <span class="stat-number" id="sessionReviewed">0</span>
                        <span class="stat-label">复习单词</span>
                    </div>
                </div>
                <button class="control-btn primary" id="restartBtn">
                    <i class="fas fa-redo"></i>
                    继续学习
                </button>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载单词数据中...</p>
        </div>
    </div>

    <!-- AI英语老师对话框 -->
    <div class="ai-chat-container" id="aiChatContainer">
        <div class="chat-header" id="chatHeader">
            <div class="chat-title">
                <i class="fas fa-robot"></i>
                <span>AI英语老师</span>
            </div>
            <div class="chat-controls">
                <button class="chat-minimize-btn" id="chatMinimizeBtn">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="chat-close-btn" id="chatCloseBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="chat-body" id="chatBody">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>你好！我是你的AI英语老师 👋</p>
                        <p>我可以帮你：</p>
                        <ul>
                            <li>详细解释当前单词的用法</li>
                            <li>提供更多例句和语法说明</li>
                            <li>回答英语学习相关问题</li>
                            <li>给出学习建议和技巧</li>
                        </ul>
                        <p>有什么想问的吗？</p>
                    </div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="quick-questions" id="quickQuestions">
                    <button class="quick-btn" data-question="explain-current">解释当前单词</button>
                    <button class="quick-btn" data-question="more-examples">更多例句</button>
                    <button class="quick-btn" data-question="grammar">语法说明</button>
                </div>
                <div class="chat-input-wrapper">
                    <input type="text" id="chatInput" placeholder="输入你的问题..." maxlength="500">
                    <button id="chatSendBtn" class="chat-send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 聊天触发按钮 -->
    <div class="chat-trigger" id="chatTrigger">
        <i class="fas fa-comments"></i>
        <span class="chat-badge" id="chatBadge" style="display: none;">1</span>
    </div>

    <!-- 登录/注册模态框 -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modalTitle">登录</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="authForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="auth-btn primary" id="authSubmitBtn">登录</button>
                        <button type="button" class="auth-btn secondary" id="authSwitchBtn">没有账号？注册</button>
                    </div>
                </form>
                <div class="auth-message" id="authMessage" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
