/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* 背景动画 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 10%;
    left: 60%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* 主容器 */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.8s ease-out;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2rem;
    color: #667eea;
}

.logo h1 {
    color: #333;
    font-weight: 600;
    font-size: 1.8rem;
}

.stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 0.9rem;
}

.stat-item i {
    color: #667eea;
}

.reset-btn {
    background: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
    border: 2px solid rgba(255, 107, 107, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.reset-btn:hover {
    background: rgba(255, 107, 107, 0.2);
    border-color: rgba(255, 107, 107, 0.4);
    transform: scale(1.1);
}

/* 模式选择 */
.mode-selector {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.mode-selector h2 {
    color: #333;
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
}

.mode-buttons {
    display: flex;
    gap: 30px;
    justify-content: center;
}

.mode-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 30px 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 200px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.mode-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.mode-btn i {
    font-size: 2rem;
}

.mode-btn span {
    font-size: 1.2rem;
    font-weight: 600;
}

.mode-btn small {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 帮助提示 */
.help-tips {
    margin-top: 40px;
    padding: 30px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.help-tips h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.tip {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #666;
}

.tip kbd {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

/* 学习界面 */
.study-container {
    animation: fadeInUp 0.8s ease-out;
}

/* 进度条 */
.progress-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-text {
    color: #666;
    font-weight: 500;
    font-size: 0.9rem;
}

.progress-percentage {
    color: #667eea;
    font-weight: 600;
    font-size: 1.1rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: rgba(102, 126, 234, 0.2);
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 5px;
    transition: width 0.5s ease;
    width: 0%;
}

/* 单词卡片 */
.word-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.mode-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.word-display {
    text-align: center;
    margin-bottom: 40px;
    perspective: 1000px; /* 3D透视效果 */
}

/* 翻转卡片容器 */
.flip-card {
    width: 100%;
    height: 300px;
    position: relative;
    cursor: pointer;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

/* 翻转状态 */
.flip-card.flipped .flip-card-inner {
    transform: rotateY(180deg);
}

/* 卡片正面和背面的共同样式 */
.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

/* 卡片正面 */
.flip-card-front {
    background: white;
}

/* 卡片背面 */
.flip-card-back {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: rotateY(180deg);
}

/* 翻转提示 */
.flip-hint {
    margin-top: 15px;
    color: #667eea;
    font-size: 0.9rem;
    opacity: 0.9;
    animation: pulse 2s infinite;
    background: rgba(102, 126, 234, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid rgba(102, 126, 234, 0.3);
    display: inline-block;
}

.flip-hint i {
    margin-right: 5px;
}

/* 例句容器 */
.examples-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 20px 0;
    box-sizing: border-box;
}

.examples-container h3 {
    margin: 0 0 15px 0;
    font-size: 1.3rem;
    color: white;
    text-align: center;
}

.examples-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 15px;
    max-height: 220px;
    overflow-y: auto;
    padding: 10px 5px;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

/* Webkit浏览器滚动条样式 */
.examples-list::-webkit-scrollbar {
    width: 6px;
}

.examples-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.examples-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.examples-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.example-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 12px;
    backdrop-filter: blur(10px);
}

.example-en {
    font-style: italic;
    margin-bottom: 5px;
    font-size: 0.95rem;
}

.example-zh {
    font-size: 0.9rem;
    opacity: 0.9;
}

.flip-back-hint {
    margin-top: 15px;
    font-size: 0.8rem;
    opacity: 0.7;
}

.flip-back-hint i {
    margin-right: 5px;
}

.english-word {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
    animation: wordPulse 0.6s ease-out;
}

.word-pronunciation {
    display: flex;
    justify-content: center;
}

.pronounce-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pronounce-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 选项样式 */
.options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 30px;
}

.option {
    background: rgba(102, 126, 234, 0.05);
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.option:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

.option-letter {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.option-text {
    color: #333;
    font-weight: 500;
    line-height: 1.4;
}

/* 选项状态 */
.option.correct {
    background: rgba(76, 175, 80, 0.1);
    border-color: #4caf50;
    animation: correctPulse 0.6s ease-out;
}

.option.incorrect {
    background: rgba(244, 67, 54, 0.1);
    border-color: #f44336;
    animation: incorrectShake 0.6s ease-out;
}

.option.disabled {
    pointer-events: none;
    opacity: 0.6;
}

/* 反馈样式 */
.feedback {
    text-align: center;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    animation: feedbackSlide 0.5s ease-out;
}

.feedback.correct {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.feedback.incorrect {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.feedback-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.feedback-icon {
    font-size: 1.5rem;
}

.feedback-text {
    font-weight: 600;
    font-size: 1.1rem;
}

/* 控制按钮 */
.controls {
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

.control-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 120px;
    justify-content: center;
}

.control-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.control-btn.secondary {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.2);
}

.control-btn:hover {
    transform: translateY(-2px);
}

.control-btn.primary:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 完成界面 */
.completion-screen {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.8s ease-out;
}

.completion-icon {
    font-size: 4rem;
    color: #ffd700;
    margin-bottom: 20px;
    animation: bounce 1s ease-out;
}

.completion-screen h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 30px;
}

.completion-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
}

.completion-stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* 动画定义 */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes wordPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes correctPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes incorrectShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

@keyframes feedbackSlide {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* AI聊天对话框样式 */
.chat-trigger {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    color: white;
    font-size: 1.5rem;
}

.chat-trigger:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.chat-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.ai-chat-container {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 380px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideInUp 0.3s ease-out;
}

.ai-chat-container.show {
    display: flex;
}

.ai-chat-container.minimized .chat-body {
    display: none;
}

.ai-chat-container.minimized {
    height: auto;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.chat-controls {
    display: flex;
    gap: 10px;
}

.chat-minimize-btn,
.chat-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.chat-minimize-btn:hover,
.chat-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(500px - 60px);
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 320px;
}

.message {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    animation: messageSlide 0.3s ease-out;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.ai-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #f1f3f4;
    color: #666;
}

.message-content {
    background: #f8f9fa;
    padding: 12px 15px;
    border-radius: 15px;
    max-width: 250px;
    line-height: 1.4;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content li {
    margin-bottom: 4px;
}

.chat-input-container {
    border-top: 1px solid #eee;
    padding: 15px;
}

.quick-questions {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.quick-btn {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1px solid rgba(102, 126, 234, 0.2);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-btn:hover {
    background: rgba(102, 126, 234, 0.2);
}

.chat-input-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

#chatInput {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 10px 15px;
    outline: none;
    font-size: 14px;
}

#chatInput:focus {
    border-color: #667eea;
}

.chat-send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
}

.chat-send-btn:hover {
    transform: scale(1.1);
}

.chat-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-style: italic;
    padding: 10px 15px;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: #667eea;
    border-radius: 50%;
    animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typingPulse {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .stats {
        flex-wrap: wrap;
        justify-content: center;
    }

    .mode-buttons {
        flex-direction: column;
        align-items: center;
    }

    .options-container {
        grid-template-columns: 1fr;
    }

    .english-word {
        font-size: 2rem;
    }

    .completion-stats {
        flex-direction: column;
        gap: 20px;
    }

    /* 移动端聊天样式调整 */
    .ai-chat-container {
        width: calc(100vw - 20px);
        right: 10px;
        bottom: 80px;
        height: 400px;
    }

    .chat-trigger {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .message-content {
        max-width: 200px;
        font-size: 14px;
    }

    .quick-questions {
        flex-direction: column;
        gap: 5px;
    }

    .quick-btn {
        width: 100%;
        text-align: center;
    }
}

/* 用户认证样式 */
.user-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

.auth-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.auth-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.auth-btn.primary {
    background: #4CAF50;
    border-color: #4CAF50;
}

.auth-btn.primary:hover {
    background: #45a049;
}

.auth-btn.secondary {
    background: transparent;
    color: #667eea;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    color: white;
    font-size: 0.9rem;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 25px;
}

.auth-message {
    padding: 12px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
}

.auth-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.auth-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
