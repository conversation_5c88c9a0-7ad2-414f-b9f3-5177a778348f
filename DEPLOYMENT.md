# 英语闪卡学习系统 - PythonAnywhere部署指南

## 项目概述

这是一个增强版的英语闪卡学习系统，现在支持：
- 用户注册和登录
- 跨设备学习进度同步
- 服务器端数据持久化
- 本地数据备份（localStorage作为备份）

## 文件结构

```
demo/
├── app.py                 # Flask主应用
├── wsgi.py               # WSGI配置文件
├── requirements.txt      # Python依赖
├── DEPLOYMENT.md         # 部署说明（本文件）
├── templates/
│   └── index.html       # 主页面模板
├── static/
│   ├── script.js        # JavaScript逻辑
│   ├── styles.css       # CSS样式
│   └── vocab_data.json  # 词汇数据
└── flashcard.db         # SQLite数据库（运行后自动创建）
```

## PythonAnywhere部署步骤

### 1. 上传文件

1. 登录PythonAnywhere控制台
2. 进入Files页面
3. 创建一个新文件夹（如 `demo`）
4. 上传以下文件到该文件夹：
   - `app.py`
   - `wsgi.py`
   - `requirements.txt`
   - `templates/` 文件夹及其内容
   - `static/` 文件夹及其内容

### 2. 安装依赖

1. 打开PythonAnywhere的Bash控制台
2. 进入项目目录：
   ```bash
   cd ~/demo
   ```
3. 安装依赖：
   ```bash
   pip3.10 install --user -r requirements.txt
   ```

### 3. 配置Web应用

1. 进入Web页面
2. 点击"Add a new web app"
3. 选择"Manual configuration"
4. 选择Python 3.10
5. 在配置页面设置：
   - **Source code**: `/home/<USER>/demo`
   - **Working directory**: `/home/<USER>/demo`
   - **WSGI configuration file**: `/home/<USER>/demo/wsgi.py`

### 4. 修改WSGI文件

编辑 `wsgi.py` 文件，将 `yourusername` 替换为您的实际用户名：

```python
path = '/home/<USER>/demo'  # 替换yourusername
```

### 5. 设置静态文件

在Web配置页面的"Static files"部分添加：
- **URL**: `/static/`
- **Directory**: `/home/<USER>/demo/static/`

### 6. 重新加载应用

点击Web配置页面顶部的绿色"Reload"按钮。

## 功能特性

### 用户认证
- 用户可以注册新账号
- 登录后学习进度会保存到服务器
- 支持跨设备同步

### 数据持久化
- 使用SQLite数据库存储用户数据
- 学习进度实时同步到服务器
- 本地localStorage作为备份

### 学习功能
- 保持原有的所有学习功能
- AI例句生成
- 语音发音
- 进度跟踪
- 复习系统

## 数据库结构

系统会自动创建以下数据表：

1. **users** - 用户信息
2. **user_progress** - 学习进度
3. **study_sessions** - 学习记录

## 安全注意事项

1. 修改 `app.py` 中的 `secret_key`：
   ```python
   app.secret_key = 'your-unique-secret-key-here'
   ```

2. 在生产环境中关闭调试模式：
   ```python
   app.run(debug=False)
   ```

## 故障排除

### 常见问题

1. **500错误**: 检查错误日志，通常是路径配置问题
2. **静态文件无法加载**: 确认静态文件路径配置正确
3. **数据库错误**: 确保应用有写入权限

### 查看日志

在PythonAnywhere的Web配置页面可以查看：
- Error log
- Server log

## 升级说明

相比原版本的改进：

1. **用户系统**: 添加了完整的用户注册/登录功能
2. **数据同步**: 学习进度可在多设备间同步
3. **数据安全**: 服务器端数据持久化，不会因清除浏览器数据而丢失
4. **向下兼容**: 未登录用户仍可使用本地存储功能

## 技术栈

- **后端**: Flask + SQLite
- **前端**: HTML5 + CSS3 + JavaScript
- **部署**: PythonAnywhere WSGI
- **数据库**: SQLite（自动创建）
