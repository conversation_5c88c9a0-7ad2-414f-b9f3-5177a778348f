# 英语闪卡学习系统 📚

一个智能的英语单词学习应用，支持AI例句生成、用户认证、进度同步和PWA安装。

## ✨ 功能特性

- 🎯 **智能学习** - AI生成例句，语音发音
- 👤 **用户系统** - 注册登录，进度同步
- 📱 **PWA支持** - 可安装为手机App
- 🔄 **离线功能** - 支持离线学习
- 📊 **进度跟踪** - 学习统计和复习系统
- 🤖 **AI助手** - 智能英语学习助手

## 🚀 快速部署

### 1. 上传文件到PythonAnywhere
```
demo/
├── app.py
├── wsgi.py  
├── requirements.txt
├── templates/
│   └── index.html
└── static/
    ├── script.js
    ├── styles.css
    ├── vocab_data.json
    ├── manifest.json
    ├── sw.js
    └── icons/ (图标文件夹)
```

### 2. 安装依赖
```bash
cd ~/demo
pip3.10 install --user -r requirements.txt
```

### 3. 配置Web应用
- **Source code**: `/home/<USER>/demo`
- **Working directory**: `/home/<USER>/demo`
- **WSGI file**: `/home/<USER>/demo/wsgi.py`
- **Static files**: URL=`/static/`, Directory=`/home/<USER>/demo/static/`

### 4. 修改wsgi.py中的用户名
```python
path = '/home/<USER>/demo'  # 替换为实际用户名
```

## 📱 App下载功能

应用支持多种安装方式：

### PWA安装（推荐）
- 访问网站后会自动提示安装
- 点击右下角绿色"安装应用"按钮
- 支持离线使用和推送通知

### 手动安装指导
- **Android Chrome**: 菜单 → "安装应用"
- **iOS Safari**: 分享 → "添加到主屏幕"

## 🔧 技术栈

- **后端**: Flask + SQLite
- **前端**: HTML5 + CSS3 + JavaScript
- **PWA**: Service Worker + Web App Manifest
- **AI**: Google Gemini API

## 📞 支持

如有问题，请检查：
1. HTTPS是否正常工作
2. 所有静态文件是否正确上传
3. 数据库权限是否正确

---

🎯 **开始学习英语，让AI助力您的语言之旅！**
