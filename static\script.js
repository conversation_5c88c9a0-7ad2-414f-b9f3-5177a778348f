class FlashcardApp {
    constructor() {
        this.vocabData = [];
        this.currentWordIndex = 0;
        this.currentMode = 'normal'; // 'normal' or 'review'
        this.currentWord = null;
        this.currentOptions = [];
        this.correctAnswer = 0;
        this.isAnswered = false;
        this.lastAnswerCorrect = false; // 跟踪最后一次答题是否正确

        // AI例句生成相关（现在使用后端API）
        this.examplesCache = new Map(); // 缓存已生成的例句

        // AI聊天相关
        this.chatHistory = [];
        this.isChatOpen = false;
        this.isChatMinimized = false;
        
        // 学习状态跟踪
        this.wordStates = new Map(); // word -> {status: 'learning'|'mastered'|'review', streak: number, lastSeen: timestamp}
        this.reviewQueue = []; // 需要复习的单词队列
        this.sessionStats = {
            reviewed: 0
        };

        // 用户认证状态
        this.isLoggedIn = false;
        this.currentUser = null;

        this.initializeApp();

        // 测试语音合成功能
        this.testSpeechSynthesis();
    }

    async initializeApp() {
        try {
            await this.checkUserStatus();
            await this.loadVocabData();
            await this.initializeWordStates();
            this.bindEvents();
            this.bindAuthEvents();
            this.updateStats();
            this.hideLoading();

            // 添加键盘快捷键支持
            this.addKeyboardSupport();
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('加载单词数据失败，请刷新页面重试');
        }
    }

    showError(message) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.innerHTML = `
            <div class="loading-spinner">
                <div class="error-icon" style="color: #ff6b6b; font-size: 3rem; margin-bottom: 20px;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p style="color: white; font-size: 1.2rem;">${message}</p>
                <button onclick="location.reload()" style="
                    background: white;
                    color: #667eea;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    margin-top: 20px;
                    cursor: pointer;
                    font-weight: 600;
                ">重新加载</button>
            </div>
        `;
    }

    addKeyboardSupport() {
        document.addEventListener('keydown', (e) => {
            // 只有在学习界面显示时才处理键盘事件
            if (document.getElementById('studyContainer').style.display !== 'none') {
                switch(e.key) {
                    case '1':
                    case 'a':
                    case 'A':
                        if (!this.isAnswered) this.selectOption(0);
                        break;
                    case '2':
                    case 'b':
                    case 'B':
                        if (!this.isAnswered) this.selectOption(1);
                        break;
                    case '3':
                    case 'c':
                    case 'C':
                        if (!this.isAnswered) this.selectOption(2);
                        break;
                    case '4':
                    case 'd':
                    case 'D':
                        if (!this.isAnswered) this.selectOption(3);
                        break;
                    case ' ':
                        e.preventDefault();
                        this.pronounceWord(); // 空格键随时可以发音
                        break;
                    case 'Enter':
                        if (this.isAnswered) {
                            this.nextWord();
                        }
                        break;
                }
            }
        });
    }

    async loadVocabData() {
        try {
            // 首先尝试加载JSON文件
            const response = await fetch('/static/vocab_data.json');
            if (response.ok) {
                this.vocabData = await response.json();
                console.log(`从JSON加载了 ${this.vocabData.length} 个单词`);
                // 随机打乱单词顺序
                this.shuffleArray(this.vocabData);
                return;
            }
        } catch (error) {
            console.log('JSON文件不存在，尝试从Excel文件加载...');
        }

        // 如果JSON文件不存在，尝试直接从Excel文件读取
        try {
            await this.loadFromExcel();
            // 随机打乱单词顺序
            this.shuffleArray(this.vocabData);
        } catch (error) {
            console.error('加载词汇数据失败:', error);
            throw error;
        }
    }

    async loadFromExcel() {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.style.display = 'none';

            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) {
                    reject(new Error('未选择文件'));
                    return;
                }

                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const data = new Uint8Array(event.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: ['word', 'meaning'] });

                        this.vocabData = jsonData.filter(item => item.word && item.meaning);
                        console.log(`从Excel加载了 ${this.vocabData.length} 个单词`);
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.readAsArrayBuffer(file);
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        });
    }

    async initializeWordStates() {
        // 如果用户已登录，从服务器加载状态
        if (this.isLoggedIn) {
            try {
                const response = await fetch('/api/load_progress');
                const result = await response.json();
                if (result.success) {
                    this.wordStates = new Map(Object.entries(result.data));
                    console.log(`从服务器加载了 ${this.wordStates.size} 个单词状态`);
                } else {
                    console.log('服务器无进度数据，使用本地数据');
                    this.loadLocalStates();
                }
            } catch (error) {
                console.error('从服务器加载进度失败，使用本地数据:', error);
                this.loadLocalStates();
            }
        } else {
            // 未登录时从localStorage加载
            this.loadLocalStates();
        }
    }

    loadLocalStates() {
        const savedStates = localStorage.getItem('flashcard_states');
        if (savedStates) {
            this.wordStates = new Map(JSON.parse(savedStates));
            console.log(`从localStorage加载了 ${this.wordStates.size} 个单词状态`);
        }

        // 初始化新单词状态
        let newWordsCount = 0;
        this.vocabData.forEach(item => {
            if (!this.wordStates.has(item.word)) {
                this.wordStates.set(item.word, {
                    status: 'learning',
                    streak: 0,
                    lastSeen: 0
                });
                newWordsCount++;
            }
        });

        console.log(`初始化了 ${newWordsCount} 个新单词状态`);
        this.updateReviewQueue();

        // 初始化后立即统计一次
        let mastered = 0, learning = 0, review = 0;
        this.wordStates.forEach(state => {
            switch (state.status) {
                case 'mastered': mastered++; break;
                case 'learning': learning++; break;
                case 'review': review++; break;
            }
        });
        console.log(`初始化完成后的统计: 已掌握=${mastered}, 学习中=${learning}, 需复习=${review}`);
    }

    updateReviewQueue() {
        this.reviewQueue = [];
        this.wordStates.forEach((state, word) => {
            if (state.status === 'review') {
                this.reviewQueue.push(word);
            }
        });
        
        // 按最后学习时间排序，最早的排在前面
        this.reviewQueue.sort((a, b) => {
            return this.wordStates.get(a).lastSeen - this.wordStates.get(b).lastSeen;
        });
    }

    async saveStates() {
        // 保存到localStorage（作为备份）
        localStorage.setItem('flashcard_states', JSON.stringify([...this.wordStates]));

        // 如果用户已登录，同时保存到服务器
        if (this.isLoggedIn) {
            try {
                const wordStatesObj = Object.fromEntries(this.wordStates);
                const response = await fetch('/api/save_progress', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        word_states: wordStatesObj
                    })
                });
                const result = await response.json();
                if (!result.success) {
                    console.error('保存到服务器失败:', result.message);
                }
            } catch (error) {
                console.error('保存到服务器失败:', error);
            }
        }

        // 同时保存学习进度
        this.saveProgress();
    }

    saveProgress() {
        const progressData = {
            normalModeIndex: this.currentMode === 'normal' ? this.currentWordIndex : this.loadProgress()?.normalModeIndex || 0,
            currentMode: this.currentMode,
            sessionStats: this.sessionStats,
            lastSaved: Date.now()
        };
        localStorage.setItem('flashcard_progress', JSON.stringify(progressData));
    }

    loadProgress() {
        const savedProgress = localStorage.getItem('flashcard_progress');
        if (savedProgress) {
            const progressData = JSON.parse(savedProgress);
            // 只在同一天内恢复进度
            const today = new Date().toDateString();
            const savedDate = new Date(progressData.lastSaved).toDateString();

            if (today === savedDate) {
                return progressData;
            }
        }
        return null;
    }

    bindEvents() {
        // 模式选择
        document.getElementById('normalModeBtn').addEventListener('click', () => {
            this.startMode('normal');
        });
        
        document.getElementById('reviewModeBtn').addEventListener('click', () => {
            this.startMode('review');
        });
        
        // 选项点击
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', (e) => {
                if (!this.isAnswered) {
                    this.selectOption(parseInt(e.currentTarget.dataset.option));
                }
            });
        });
        
        // 控制按钮
        document.getElementById('backBtn').addEventListener('click', () => {
            this.showModeSelector();
        });
        
        document.getElementById('nextBtn').addEventListener('click', () => {
            this.nextWord();
        });
        
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.showModeSelector();
        });
        
        // 发音按钮
        document.getElementById('pronounceBtn').addEventListener('click', () => {
            this.pronounceWord();
        });

        // 重置进度按钮
        document.getElementById('resetProgressBtn').addEventListener('click', () => {
            this.resetAllProgress();
        });

        // AI聊天相关事件
        this.bindChatEvents();
    }

    startMode(mode) {
        this.currentMode = mode;

        // 加载保存的进度
        const savedProgress = this.loadProgress();

        if (mode === 'normal') {
            // 正常模式：使用保存的正常模式进度
            if (savedProgress && savedProgress.normalModeIndex !== undefined) {
                const continueProgress = confirm(`检测到您之前的学习进度，是否继续？`);
                if (continueProgress) {
                    this.currentWordIndex = savedProgress.normalModeIndex;
                } else {
                    this.currentWordIndex = 0;
                }
            } else {
                this.currentWordIndex = 0;
            }
            this.sessionStats = { reviewed: 0 };
        } else {
            // 复习模式：从0开始复习队列
            this.currentWordIndex = 0;
            this.sessionStats = savedProgress?.sessionStats || { reviewed: 0 };
        }

        if (mode === 'review' && this.reviewQueue.length === 0) {
            alert('没有需要复习的单词！');
            return;
        }

        this.showStudyContainer();
        this.updateModeIndicator();
        this.loadNextWord();
    }

    showModeSelector() {
        document.getElementById('modeSelector').style.display = 'block';
        document.getElementById('studyContainer').style.display = 'none';
        document.getElementById('completionScreen').style.display = 'none';
    }

    showStudyContainer() {
        document.getElementById('modeSelector').style.display = 'none';
        document.getElementById('studyContainer').style.display = 'block';
        document.getElementById('completionScreen').style.display = 'none';
    }

    showCompletionScreen() {
        document.getElementById('modeSelector').style.display = 'none';
        document.getElementById('studyContainer').style.display = 'none';
        document.getElementById('completionScreen').style.display = 'block';

        document.getElementById('sessionReviewed').textContent = this.sessionStats.reviewed;

        // 播放完成音效（如果支持）
        this.playCompletionSound();

        // 保存学习记录
        this.saveSessionRecord();
    }

    playCompletionSound() {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance('Congratulations!');
            utterance.lang = 'en-US';
            utterance.rate = 0.8;
            utterance.volume = 0.5;
            speechSynthesis.speak(utterance);
        }
    }

    saveSessionRecord() {
        const today = new Date().toDateString();
        const records = JSON.parse(localStorage.getItem('flashcard_records') || '{}');

        if (!records[today]) {
            records[today] = { reviewed: 0, sessions: 0 };
        }

        records[today].reviewed += this.sessionStats.reviewed;
        records[today].sessions += 1;

        localStorage.setItem('flashcard_records', JSON.stringify(records));
    }

    updateModeIndicator() {
        const indicator = document.getElementById('modeIndicator');
        if (this.currentMode === 'review') {
            indicator.innerHTML = '<i class="fas fa-redo"></i><span>复习模式</span>';
        } else {
            indicator.innerHTML = '<i class="fas fa-play"></i><span>正常模式</span>';
        }
    }

    loadNextWord() {
        let wordToStudy;

        if (this.currentMode === 'review') {
            if (this.currentWordIndex >= this.reviewQueue.length) {
                this.showCompletionScreen();
                return;
            }
            const wordText = this.reviewQueue[this.currentWordIndex];
            wordToStudy = this.vocabData.find(item => item.word === wordText);
        } else {
            // 正常模式：直接学习下一个新单词
            let found = false;
            for (let i = this.currentWordIndex; i < this.vocabData.length; i++) {
                const word = this.vocabData[i];
                // 学习所有单词，不跳过任何状态
                wordToStudy = word;
                this.currentWordIndex = i;
                found = true;
                break;
            }

            if (!found) {
                this.showCompletionScreen();
                return;
            }
        }

        this.currentWord = wordToStudy;
        this.generateOptions();
        this.displayWord();
        this.updateProgress();
        this.isAnswered = false;

        // 隐藏反馈和下一步按钮
        document.getElementById('feedback').style.display = 'none';
        document.getElementById('nextBtn').style.display = 'none';

        // 重置选项状态
        document.querySelectorAll('.option').forEach(option => {
            option.classList.remove('correct', 'incorrect', 'disabled');
        });
    }

    generateOptions() {
        const correctMeaning = this.currentWord.meaning;
        const options = [correctMeaning];
        
        // 随机选择3个错误选项
        const otherWords = this.vocabData.filter(item => item.word !== this.currentWord.word);
        const shuffled = otherWords.sort(() => Math.random() - 0.5);
        
        for (let i = 0; i < 3 && i < shuffled.length; i++) {
            options.push(shuffled[i].meaning);
        }
        
        // 打乱选项顺序
        this.currentOptions = options.sort(() => Math.random() - 0.5);
        this.correctAnswer = this.currentOptions.indexOf(correctMeaning);
    }

    displayWord() {
        document.getElementById('englishWord').textContent = this.currentWord.word;

        this.currentOptions.forEach((option, index) => {
            document.getElementById(`option${index}`).textContent = option;
        });

        // 重置翻转卡片状态
        const flipCard = document.getElementById('flipCard');
        flipCard.classList.remove('flipped');

        // 隐藏翻转提示
        document.getElementById('flipHint').style.display = 'none';

        // 重置答题状态
        this.lastAnswerCorrect = false;

        // 准备例句数据
        this.setupExamples();

        // 设置翻转事件监听器
        this.setupFlipCardEvents();
    }

    selectOption(selectedIndex) {
        this.isAnswered = true;
        const isCorrect = selectedIndex === this.correctAnswer;
        this.lastAnswerCorrect = isCorrect; // 记录答题结果

        // 显示所有选项的状态
        document.querySelectorAll('.option').forEach((option, index) => {
            option.classList.add('disabled');
            if (index === this.correctAnswer) {
                option.classList.add('correct');
            } else if (index === selectedIndex && !isCorrect) {
                option.classList.add('incorrect');
            }
        });

        // 更新单词状态
        this.updateWordState(isCorrect);

        // 显示反馈
        this.showFeedback(isCorrect);

        // 显示下一步按钮
        document.getElementById('nextBtn').style.display = 'block';

        // 立即更新统计和掌握率
        this.updateStats();

        // 生成并显示例句
        this.generateAndShowExamples();
    }

    updateWordState(isCorrect) {
        const word = this.currentWord.word;
        const state = this.wordStates.get(word);

        if (isCorrect) {
            state.streak++;
            state.lastSeen = Date.now();

            if (state.status === 'review') {
                // 从复习状态答对，恢复到学习状态
                state.status = 'learning';
                this.sessionStats.reviewed++;
            }
        } else {
            // 答错了，加入复习队列
            state.streak = 0;
            state.status = 'review';
            state.lastSeen = Date.now();

            // 添加到复习队列（如果不在其中）
            if (!this.reviewQueue.includes(word)) {
                this.reviewQueue.push(word);
            }
        }

        this.wordStates.set(word, state);
        this.saveStates();

        // 更新统计显示
        this.updateStats();
    }

    showFeedback(isCorrect) {
        const feedback = document.getElementById('feedback');
        const feedbackContent = feedback.querySelector('.feedback-content');

        if (isCorrect) {
            feedback.className = 'feedback correct';
            feedbackContent.innerHTML = '<i class="fas fa-check-circle feedback-icon"></i><span class="feedback-text">回答正确！</span>';
            // 显示翻转提示
            this.showFlipHint();
        } else {
            feedback.className = 'feedback incorrect';
            feedbackContent.innerHTML = `<i class="fas fa-times-circle feedback-icon"></i><span class="feedback-text">正确答案是：${this.currentWord.meaning}</span>`;
        }

        feedback.style.display = 'block';
    }

    nextWord() {
        // 在正常模式下，总是前进到下一个单词
        if (this.currentMode === 'normal') {
            this.currentWordIndex++;
        } else if (this.currentMode === 'review') {
            this.currentWordIndex++;
        }

        this.saveProgress();
        this.loadNextWord();
    }

   
    updateProgress() {
    let currentForProgress, total, currentForDisplay;

    if (this.currentMode === 'review') {
        // 复习模式的逻辑 (已在上一问中修正)
        total = this.reviewQueue.length;
        currentForProgress = this.currentWordIndex;
        currentForDisplay = this.currentWordIndex + 1;
    } else {
        // [修正] 正常模式的逻辑
        total = this.vocabData.length;

        // 进度应该基于当前的单词索引，而不是总的学习状态
        // 这能正确反映用户选择“继续”或“重新开始”的意图
        currentForProgress = this.currentWordIndex;
        currentForDisplay = this.currentWordIndex + 1;
    }

    // 边界情况处理：确保在完成时数字不会超过总数
    if (total > 0 && currentForDisplay > total) {
        currentForDisplay = total;
    }

    // 更新界面显示的数字
    document.getElementById('currentWord').textContent = currentForDisplay;
    document.getElementById('totalWords').textContent = total;

    // 使用正确的“已完成数”来计算进度条百分比
    const progress = total > 0 ? (currentForProgress / total) * 100 : 0;
    const progressPercentage = Math.round(progress);

    document.getElementById('progressFill').style.width = `${progress}%`;
    document.getElementById('progressPercentage').textContent = `${progressPercentage}%`;
}



    updateStats() {
        let learning = 0;
        let review = 0;

        this.wordStates.forEach((state) => {
            switch (state.status) {
                case 'learning':
                    learning++;
                    break;
                case 'review':
                    review++;
                    break;
            }
        });

        document.getElementById('learningCount').textContent = learning;
        document.getElementById('reviewCount').textContent = review;
    }

    pronounceWord() {
        console.log('pronounceWord called'); // 调试信息

        if (!this.currentWord || !this.currentWord.word) {
            console.error('No current word to pronounce');
            return;
        }

        if ('speechSynthesis' in window) {
            try {
                // 停止当前正在播放的语音
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(this.currentWord.word);

                // 尝试选择英语语音
                const voices = speechSynthesis.getVoices();
                const englishVoice = voices.find(voice =>
                    voice.lang.startsWith('en') && voice.localService
                ) || voices.find(voice => voice.lang.startsWith('en'));

                if (englishVoice) {
                    utterance.voice = englishVoice;
                    console.log('Using voice:', englishVoice.name);
                }

                utterance.lang = 'en-US';
                utterance.rate = 0.8;
                utterance.volume = 1.0;
                utterance.pitch = 1.0;

                // 添加事件监听器用于调试
                utterance.onstart = () => {
                    console.log('Speech started:', this.currentWord.word);
                };

                utterance.onend = () => {
                    console.log('Speech ended');
                };

                utterance.onerror = (event) => {
                    console.error('Speech error:', event.error);
                };

                console.log('Speaking word:', this.currentWord.word);

                // 确保语音合成器准备就绪
                if (speechSynthesis.paused) {
                    speechSynthesis.resume();
                }

                speechSynthesis.speak(utterance);

                // 添加视觉反馈
                this.showPronunciationFeedback();

            } catch (error) {
                console.error('Error in pronounceWord:', error);
                this.showPronunciationFeedback();
            }
        } else {
            console.error('Speech synthesis not supported in this browser');
            this.showPronunciationFeedback();
            alert('您的浏览器不支持语音合成功能');
        }
    }

    // 显示发音视觉反馈
    showPronunciationFeedback() {
        const pronounceBtn = document.getElementById('pronounceBtn');
        const originalText = pronounceBtn.innerHTML;

        // 改变按钮样式表示正在发音
        pronounceBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        pronounceBtn.style.transform = 'scale(1.1)';
        pronounceBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

        // 2秒后恢复原样
        setTimeout(() => {
            pronounceBtn.innerHTML = originalText;
            pronounceBtn.style.transform = '';
            pronounceBtn.style.background = '';
        }, 2000);
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    resetAllProgress() {
        const confirmReset = confirm('确定要重置所有学习进度吗？这将清除所有已掌握的单词和学习记录。');
        if (confirmReset) {
            // 清除所有本地存储数据
            localStorage.removeItem('flashcard_states');
            localStorage.removeItem('flashcard_progress');
            localStorage.removeItem('flashcard_records');

            // 重新初始化状态
            this.wordStates.clear();
            this.reviewQueue = [];
            this.currentWordIndex = 0;
            this.sessionStats = { reviewed: 0 };

            // 重新初始化单词状态
            this.initializeWordStates();

            // 更新显示
            this.updateStats();

            // 返回模式选择界面
            this.showModeSelector();

            alert('学习进度已重置！');
        }
    }

    // Fisher-Yates洗牌算法 - 随机打乱数组顺序
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            let j = Math.floor(Math.random() * (i + 1));
            // 交换元素 array[i] 和 array[j]
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    // 设置例句显示
    setupExamples() {
        const examplesList = document.getElementById('examplesList');
        examplesList.innerHTML = '';

        // 检查当前单词是否有例句
        if (this.currentWord.examples && this.currentWord.examples.length > 0) {
            this.currentWord.examples.forEach(example => {
                const exampleItem = document.createElement('div');
                exampleItem.className = 'example-item';
                exampleItem.innerHTML = `
                    <div class="example-en">"${example.en}"</div>
                    <div class="example-zh">${example.zh}</div>
                `;
                examplesList.appendChild(exampleItem);
            });
        } else {
            // 如果没有例句，显示默认信息
            const noExamples = document.createElement('div');
            noExamples.className = 'example-item';
            noExamples.innerHTML = `
                <div class="example-en">暂无例句</div>
                <div class="example-zh">该单词暂时没有配置例句</div>
            `;
            examplesList.appendChild(noExamples);
        }
    }

    // 设置翻转卡片事件
    setupFlipCardEvents() {
        const flipCard = document.getElementById('flipCard');

        // 移除之前的翻转事件监听器（但保留发音按钮事件）
        const newFlipCard = flipCard.cloneNode(true);
        flipCard.parentNode.replaceChild(newFlipCard, flipCard);

        // 重新绑定发音按钮事件
        const pronounceBtn = document.getElementById('pronounceBtn');
        pronounceBtn.addEventListener('click', (event) => {
            event.stopPropagation(); // 阻止事件冒泡到翻转卡片
            this.pronounceWord();
        });

        // 添加翻转事件，但只有在答对且显示提示后才允许翻转
        newFlipCard.addEventListener('click', (event) => {
            // 如果点击的是发音按钮，不执行翻转
            if (event.target.closest('.pronounce-btn')) {
                return;
            }

            // 只有答对且显示了提示才允许翻转
            const flipHint = document.getElementById('flipHint');
            if (this.isAnswered && this.lastAnswerCorrect && flipHint.style.display !== 'none') {
                newFlipCard.classList.toggle('flipped');
            }
        });
    }

    // 显示翻转提示（答对后显示）
    showFlipHint() {
        const flipHint = document.getElementById('flipHint');
        flipHint.style.display = 'block';
    }

    // 测试语音合成功能
    testSpeechSynthesis() {
        console.log('Testing speech synthesis...');

        if ('speechSynthesis' in window) {
            console.log('Speech synthesis is supported');

            // 等待语音列表加载
            const loadVoices = () => {
                const voices = speechSynthesis.getVoices();
                console.log('Available voices:', voices.length);

                if (voices.length > 0) {
                    const englishVoices = voices.filter(voice => voice.lang.startsWith('en'));
                    console.log('English voices:', englishVoices.length);

                    if (englishVoices.length > 0) {
                        console.log('First English voice:', englishVoices[0]);
                    }
                }
            };

            // 语音列表可能需要时间加载
            if (speechSynthesis.getVoices().length > 0) {
                loadVoices();
            } else {
                speechSynthesis.onvoiceschanged = loadVoices;
            }
        } else {
            console.error('Speech synthesis not supported');
        }
    }

    // 生成并显示AI例句
    async generateAndShowExamples() {
        const word = this.currentWord.word;
        const meaning = this.currentWord.meaning;

        // 检查缓存
        if (this.examplesCache.has(word)) {
            this.displayExamples(this.examplesCache.get(word));
            return;
        }

        // 显示加载状态
        this.showExamplesLoading();

        try {
            const examples = await this.callExamplesAPI(word, meaning);
            this.examplesCache.set(word, examples);
            this.displayExamples(examples);
        } catch (error) {
            console.error('生成例句失败:', error);
            this.showExamplesError();
        }
    }

    // 调用后端API生成例句
    async callExamplesAPI(word, meaning) {
        const response = await fetch('/api/generate_examples', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                word: word,
                meaning: meaning
            })
        });

        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || '生成例句失败');
        }

        return data.examples;
    }

    // 显示例句加载状态
    showExamplesLoading() {
        const examplesList = document.getElementById('examplesList');
        examplesList.innerHTML = `
            <div class="loading-message" style="text-align: center; padding: 20px;">
                <div style="margin-bottom: 10px;">📝 AI例句生成中...</div>
                <div class="loading-spinner" style="margin: 10px auto; width: 30px; height: 30px;">
                    <div style="border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 100%; height: 100%; animation: spin 1s linear infinite;"></div>
                </div>
            </div>
        `;
    }

    // 显示例句错误状态
    showExamplesError() {
        const examplesList = document.getElementById('examplesList');
        examplesList.innerHTML = `
            <div class="error-message" style="text-align: center; padding: 20px; color: #dc3545;">
                <div style="margin-bottom: 10px;">❌ 例句生成失败</div>
                <div style="font-size: 14px; color: #666;">网络连接或API调用出现问题，请稍后重试。</div>
            </div>
        `;
    }

    // 显示生成的例句
    displayExamples(examples) {
        const examplesHtml = examples.map((example) => `
            <div class="example-item" style="margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <div class="example-en" style="font-size: 16px; font-weight: 500; color: #333; margin-bottom: 5px;">
                    ${example.en}
                </div>
                <div class="example-zh" style="font-size: 14px; color: #666;">
                    ${example.zh}
                </div>
            </div>
        `).join('');

        const examplesList = document.getElementById('examplesList');
        examplesList.innerHTML = examplesHtml;
    }

    // 绑定聊天相关事件
    bindChatEvents() {
        const chatTrigger = document.getElementById('chatTrigger');
        const chatCloseBtn = document.getElementById('chatCloseBtn');
        const chatMinimizeBtn = document.getElementById('chatMinimizeBtn');
        const chatSendBtn = document.getElementById('chatSendBtn');
        const chatInput = document.getElementById('chatInput');
        const quickQuestions = document.querySelectorAll('.quick-btn');

        // 打开/关闭聊天
        chatTrigger.addEventListener('click', () => {
            this.toggleChat();
        });

        chatCloseBtn.addEventListener('click', () => {
            this.closeChat();
        });

        chatMinimizeBtn.addEventListener('click', () => {
            this.minimizeChat();
        });

        // 发送消息
        chatSendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 快捷问题
        quickQuestions.forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleQuickQuestion(btn.dataset.question);
            });
        });
    }

    // 切换聊天窗口
    toggleChat() {
        const chatContainer = document.getElementById('aiChatContainer');
        this.isChatOpen = !this.isChatOpen;

        if (this.isChatOpen) {
            chatContainer.classList.add('show');
            if (this.isChatMinimized) {
                this.isChatMinimized = false;
                chatContainer.classList.remove('minimized');
            }
        } else {
            chatContainer.classList.remove('show');
        }
    }

    // 关闭聊天
    closeChat() {
        const chatContainer = document.getElementById('aiChatContainer');
        chatContainer.classList.remove('show');
        this.isChatOpen = false;
        this.isChatMinimized = false;
    }

    // 最小化聊天
    minimizeChat() {
        const chatContainer = document.getElementById('aiChatContainer');
        this.isChatMinimized = !this.isChatMinimized;

        if (this.isChatMinimized) {
            chatContainer.classList.add('minimized');
        } else {
            chatContainer.classList.remove('minimized');
        }
    }

    // 发送消息
    async sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();

        if (!message) return;

        // 添加用户消息
        this.addMessage(message, 'user');
        chatInput.value = '';

        // 显示输入状态
        this.showTypingIndicator();

        try {
            // 调用AI API
            const response = await this.callChatAPI(message);
            this.hideTypingIndicator();
            this.addMessage(response, 'ai');
        } catch (error) {
            console.error('聊天API调用失败:', error);
            this.hideTypingIndicator();
            this.addMessage('抱歉，我现在无法回答您的问题。请稍后再试。', 'ai');
        }
    }

    // 处理快捷问题
    async handleQuickQuestion(questionType) {
        if (!this.currentWord) {
            this.addMessage('请先开始学习单词，然后我就可以为您详细解释了！', 'ai');
            return;
        }

        let question = '';
        switch (questionType) {
            case 'explain-current':
                question = `请详细解释单词"${this.currentWord.word}"的用法、词性、常见搭配等`;
                break;
            case 'more-examples':
                question = `请为单词"${this.currentWord.word}"提供更多例句，包括不同语境下的用法`;
                break;
            case 'grammar':
                question = `请解释单词"${this.currentWord.word}"的语法用法和注意事项`;
                break;
        }

        // 添加用户问题
        this.addMessage(question, 'user');

        // 显示输入状态
        this.showTypingIndicator();

        try {
            const response = await this.callChatAPI(question);
            this.hideTypingIndicator();
            this.addMessage(response, 'ai');
        } catch (error) {
            console.error('聊天API调用失败:', error);
            this.hideTypingIndicator();
            this.addMessage('抱歉，我现在无法回答您的问题。请稍后再试。', 'ai');
        }
    }

    // 添加消息到聊天界面
    addMessage(content, sender) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = sender === 'ai' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = this.formatMessageContent(content);

        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);
        chatMessages.appendChild(messageDiv);

        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 格式化消息内容
    formatMessageContent(content) {
        // 简单的格式化：将换行符转换为<br>，保持基本格式
        return content.replace(/\n/g, '<br>');
    }

    // 调用聊天API
    async callChatAPI(userMessage) {
        const currentWord = this.currentWord ? {
            word: this.currentWord.word,
            meaning: this.currentWord.meaning
        } : {};

        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: userMessage,
                current_word: currentWord
            })
        });

        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || '聊天失败');
        }

        return data.response;
    }

    // 显示输入指示器
    showTypingIndicator() {
        const chatMessages = document.getElementById('chatMessages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-indicator';
        typingDiv.id = 'typingIndicator';

        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <span>正在思考</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;

        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 隐藏输入指示器
    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    // 用户认证相关方法
    async checkUserStatus() {
        try {
            const response = await fetch('/api/user_status');
            const result = await response.json();
            if (result.logged_in) {
                this.isLoggedIn = true;
                this.currentUser = result.username;
                this.updateUserInterface(true);
            } else {
                this.isLoggedIn = false;
                this.currentUser = null;
                this.updateUserInterface(false);
            }
        } catch (error) {
            console.error('检查用户状态失败:', error);
            this.isLoggedIn = false;
            this.updateUserInterface(false);
        }
    }

    updateUserInterface(isLoggedIn) {
        const authButtons = document.getElementById('authButtons');
        const userInfo = document.getElementById('userInfo');
        const usernameDisplay = document.getElementById('usernameDisplay');

        if (isLoggedIn) {
            authButtons.style.display = 'none';
            userInfo.style.display = 'flex';
            usernameDisplay.textContent = this.currentUser;
        } else {
            authButtons.style.display = 'flex';
            userInfo.style.display = 'none';
        }
    }

    bindAuthEvents() {
        // 登录按钮
        document.getElementById('loginBtn').addEventListener('click', () => {
            this.showAuthModal('login');
        });

        // 注册按钮
        document.getElementById('registerBtn').addEventListener('click', () => {
            this.showAuthModal('register');
        });

        // 登出按钮
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // 模态框关闭
        document.getElementById('modalClose').addEventListener('click', () => {
            this.hideAuthModal();
        });

        // 模态框背景点击关闭
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target.id === 'modalOverlay') {
                this.hideAuthModal();
            }
        });

        // 认证表单提交
        document.getElementById('authForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAuthSubmit();
        });

        // 切换登录/注册
        document.getElementById('authSwitchBtn').addEventListener('click', () => {
            this.switchAuthMode();
        });
    }

    showAuthModal(mode) {
        const modal = document.getElementById('modalOverlay');
        const title = document.getElementById('modalTitle');
        const submitBtn = document.getElementById('authSubmitBtn');
        const switchBtn = document.getElementById('authSwitchBtn');

        if (mode === 'login') {
            title.textContent = '登录';
            submitBtn.textContent = '登录';
            switchBtn.textContent = '没有账号？注册';
        } else {
            title.textContent = '注册';
            submitBtn.textContent = '注册';
            switchBtn.textContent = '已有账号？登录';
        }

        modal.dataset.mode = mode;
        modal.style.display = 'flex';
        document.getElementById('username').focus();
    }

    hideAuthModal() {
        document.getElementById('modalOverlay').style.display = 'none';
        document.getElementById('authForm').reset();
        document.getElementById('authMessage').style.display = 'none';
    }

    switchAuthMode() {
        const modal = document.getElementById('modalOverlay');
        const currentMode = modal.dataset.mode;
        const newMode = currentMode === 'login' ? 'register' : 'login';
        this.showAuthModal(newMode);
    }

    async handleAuthSubmit() {
        const modal = document.getElementById('modalOverlay');
        const mode = modal.dataset.mode;
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const messageDiv = document.getElementById('authMessage');

        if (!username || !password) {
            this.showAuthMessage('请填写用户名和密码', 'error');
            return;
        }

        try {
            const endpoint = mode === 'login' ? '/api/login' : '/api/register';
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const result = await response.json();

            if (result.success) {
                this.showAuthMessage(result.message, 'success');
                setTimeout(() => {
                    this.hideAuthModal();
                    this.checkUserStatus();
                    // 重新加载用户数据
                    this.initializeWordStates();
                }, 1000);
            } else {
                this.showAuthMessage(result.message, 'error');
            }
        } catch (error) {
            console.error('认证失败:', error);
            this.showAuthMessage('网络错误，请重试', 'error');
        }
    }

    showAuthMessage(message, type) {
        const messageDiv = document.getElementById('authMessage');
        messageDiv.textContent = message;
        messageDiv.className = `auth-message ${type}`;
        messageDiv.style.display = 'block';
    }

    async logout() {
        try {
            const response = await fetch('/api/logout', {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                this.isLoggedIn = false;
                this.currentUser = null;
                this.updateUserInterface(false);
                // 清除本地状态，重新初始化
                this.wordStates.clear();
                this.initializeWordStates();
                alert('已成功登出');
            }
        } catch (error) {
            console.error('登出失败:', error);
            alert('登出失败，请重试');
        }
    }

}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new FlashcardApp();
});
