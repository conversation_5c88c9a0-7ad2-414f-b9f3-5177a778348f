import sqlite3
import os

print("开始查看数据库...")

# 检查数据库文件是否存在
db_file = 'flashcard.db'
if not os.path.exists(db_file):
    print(f"数据库文件 {db_file} 不存在")
    exit()

print(f"找到数据库文件: {db_file}")

try:
    # 连接数据库
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    print("成功连接到数据库")

    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()

    print(f"\n=== 数据库中的表 (共{len(tables)}个) ===")
    for table in tables:
        print(f"- {table[0]}")

    print("\n" + "="*60)

    # 查看每个表的数据
    for table in tables:
        table_name = table[0]
        print(f"\n=== 表: {table_name} ===")

        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name});")
        columns = cursor.fetchall()
        print("表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")

        # 获取总行数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
        count = cursor.fetchone()[0]
        print(f"\n总行数: {count}")

        # 获取数据
        if count > 0:
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 5;")
            rows = cursor.fetchall()

            print(f"\n数据 (前5条):")
            # 打印列名
            column_names = [col[1] for col in columns]
            print("  " + " | ".join(column_names))
            print("  " + "-" * (len(" | ".join(column_names)) + 10))

            # 打印数据
            for row in rows:
                print("  " + " | ".join(str(item) for item in row))
        else:
            print("\n  (表中无数据)")

        print("-" * 60)

    conn.close()
    print("\n✅ 数据库查看完成!")

except sqlite3.Error as e:
    print(f"❌ SQLite 错误: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")

print("脚本执行结束")
