<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>英语闪卡学习系统</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="智能英语单词学习应用，支持AI例句生成和进度同步">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="英语闪卡">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="msapplication-tap-highlight" content="no">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="{{ url_for('static', filename='icons/icon-152x152.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='icons/icon-192x192.png') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='icons/icon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='icons/icon-16x16.png') }}">

    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- 离线状态指示器 -->
    <div id="offlineIndicator" class="offline-indicator">
        <i class="fas fa-wifi"></i>
        <span>离线模式 - 部分功能受限</span>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                <h1>英语闪卡</h1>
            </div>
            <div class="user-section" id="userSection">
                <!-- 未登录状态 -->
                <div class="auth-buttons" id="authButtons">
                    <button class="auth-btn" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </button>
                    <button class="auth-btn" id="registerBtn">
                        <i class="fas fa-user-plus"></i>
                        注册
                    </button>
                </div>
                <!-- 已登录状态 -->
                <div class="user-info" id="userInfo" style="display: none;">
                    <span class="welcome-text">欢迎, <span id="usernameDisplay"></span></span>
                    <button class="auth-btn" id="logoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        登出
                    </button>
                </div>
            </div>
            <div class="stats">
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span>学习中: <span id="learningCount">0</span></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-redo"></i>
                    <span>需复习: <span id="reviewCount">0</span></span>
                </div>
                <button class="reset-btn" id="resetProgressBtn" title="重置所有学习进度">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
        </header>

        <!-- App下载区域 - 隐藏大的安装区域 -->
        <div class="app-download-section" id="appDownloadSection" style="display: none;">
            <div class="download-card">
                <div class="download-header">
                    <div class="download-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="download-info">
                        <h3>📱 安装手机App</h3>
                        <p>获得更好的学习体验，支持离线使用</p>
                    </div>
                </div>

                <div class="download-options">
                    <button class="download-btn primary" id="installPWABtn">
                        <i class="fas fa-download"></i>
                        <span>立即安装</span>
                        <small>推荐方式</small>
                    </button>

                    <button class="download-btn secondary" id="showGuideBtn">
                        <i class="fas fa-question-circle"></i>
                        <span>安装指导</span>
                        <small>手动安装</small>
                    </button>
                </div>

                <div class="download-features">
                    <div class="feature-item">
                        <i class="fas fa-wifi"></i>
                        <span>离线学习</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-bell"></i>
                        <span>学习提醒</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-sync"></i>
                        <span>进度同步</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模式选择 -->
        <div class="mode-selector" id="modeSelector">
            <h2>选择学习模式</h2>
            <div class="mode-buttons">
                <button class="mode-btn" id="normalModeBtn">
                    <i class="fas fa-play"></i>
                    <span>正常学习</span>
                    <small>学习新单词</small>
                </button>
                <button class="mode-btn" id="reviewModeBtn">
                    <i class="fas fa-redo"></i>
                    <span>复习模式</span>
                    <small>复习错误单词</small>
                </button>
            </div>

            <!-- 帮助提示 -->
            <div class="help-tips">
                <h3><i class="fas fa-keyboard"></i> 快捷键提示</h3>
                <div class="tips-grid">
                    <div class="tip">
                        <kbd>1/A</kbd> <span>选择选项A</span>
                    </div>
                    <div class="tip">
                        <kbd>2/B</kbd> <span>选择选项B</span>
                    </div>
                    <div class="tip">
                        <kbd>3/C</kbd> <span>选择选项C</span>
                    </div>
                    <div class="tip">
                        <kbd>4/D</kbd> <span>选择选项D</span>
                    </div>
                    <div class="tip">
                        <kbd>空格</kbd> <span>发音</span>
                    </div>
                    <div class="tip">
                        <kbd>回车</kbd> <span>下一个</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习界面 -->
        <div class="study-container" id="studyContainer" style="display: none;">
            <!-- 进度条 -->
            <div class="progress-container">
                <div class="progress-info">
                    <div class="progress-text">
                        <span>进度: <span id="currentWord">1</span> / <span id="totalWords">2220</span></span>
                    </div>
                    <div class="progress-percentage" id="progressPercentage">0%</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>

            <!-- 单词卡片 -->
            <div class="word-card" id="wordCard">
                <div class="word-header">
                    <div class="mode-indicator" id="modeIndicator">
                        <i class="fas fa-play"></i>
                        <span>正常模式</span>
                    </div>
                </div>
                
                <div class="word-display">
                    <div class="flip-card" id="flipCard">
                        <div class="flip-card-inner">
                            <!-- 正面：单词和发音 -->
                            <div class="flip-card-front">
                                <h2 class="english-word" id="englishWord">Loading...</h2>
                                <div class="word-pronunciation" id="pronunciation">
                                    <button class="pronounce-btn" id="pronounceBtn">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                </div>
                                <div class="flip-hint" id="flipHint" style="display: none;">
                                    <i class="fas fa-hand-pointer"></i>
                                    <span>点击单词查看例句</span>
                                </div>
                            </div>
                            <!-- 背面：例句 -->
                            <div class="flip-card-back">
                                <div class="examples-container" id="examplesContainer">
                                    <h3>📝 AI生成例句</h3>
                                    <div class="examples-list" id="examplesList">
                                        <!-- 例句将通过AI实时生成 -->
                                    </div>
                                    <div class="flip-back-hint">
                                        <i class="fas fa-undo"></i>
                                        <span>点击返回</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="options-container" id="optionsContainer">
                    <div class="option" data-option="0">
                        <span class="option-letter">A</span>
                        <span class="option-text" id="option0">选项A</span>
                    </div>
                    <div class="option" data-option="1">
                        <span class="option-letter">B</span>
                        <span class="option-text" id="option1">选项B</span>
                    </div>
                    <div class="option" data-option="2">
                        <span class="option-letter">C</span>
                        <span class="option-text" id="option2">选项C</span>
                    </div>
                    <div class="option" data-option="3">
                        <span class="option-letter">D</span>
                        <span class="option-text" id="option3">选项D</span>
                    </div>
                </div>

                <div class="feedback" id="feedback" style="display: none;">
                    <div class="feedback-content">
                        <i class="feedback-icon"></i>
                        <span class="feedback-text"></span>
                    </div>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="controls">
                <button class="control-btn secondary" id="backBtn">
                    <i class="fas fa-arrow-left"></i>
                    返回
                </button>
                <button class="control-btn primary" id="nextBtn" style="display: none;">
                    <i class="fas fa-arrow-right"></i>
                    下一个
                </button>
            </div>
        </div>

        <!-- 完成界面 -->
        <div class="completion-screen" id="completionScreen" style="display: none;">
            <div class="completion-content">
                <div class="completion-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h2>恭喜完成！</h2>
                <div class="completion-stats">
                    <div class="completion-stat">
                        <span class="stat-number" id="sessionReviewed">0</span>
                        <span class="stat-label">复习单词</span>
                    </div>
                </div>
                <button class="control-btn primary" id="restartBtn">
                    <i class="fas fa-redo"></i>
                    继续学习
                </button>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载单词数据中...</p>
        </div>
    </div>

    <!-- AI英语老师对话框 -->
    <div class="ai-chat-container" id="aiChatContainer">
        <div class="chat-header" id="chatHeader">
            <div class="chat-title">
                <i class="fas fa-robot"></i>
                <span>AI英语老师</span>
            </div>
            <div class="chat-controls">
                <button class="chat-minimize-btn" id="chatMinimizeBtn">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="chat-close-btn" id="chatCloseBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="chat-body" id="chatBody">
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>你好！我是你的AI英语老师 👋</p>
                        <p>我可以帮你：</p>
                        <ul>
                            <li>详细解释当前单词的用法</li>
                            <li>提供更多例句和语法说明</li>
                            <li>回答英语学习相关问题</li>
                            <li>给出学习建议和技巧</li>
                        </ul>
                        <p>有什么想问的吗？</p>
                    </div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="quick-questions" id="quickQuestions">
                    <button class="quick-btn" data-question="explain-current">解释当前单词</button>
                    <button class="quick-btn" data-question="more-examples">更多例句</button>
                    <button class="quick-btn" data-question="grammar">语法说明</button>
                </div>
                <div class="chat-input-wrapper">
                    <input type="text" id="chatInput" placeholder="输入你的问题..." maxlength="500">
                    <button id="chatSendBtn" class="chat-send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 聊天触发按钮 -->
    <div class="chat-trigger" id="chatTrigger">
        <i class="fas fa-comments"></i>
        <span class="chat-badge" id="chatBadge" style="display: none;">1</span>
    </div>

    <!-- 添加到主屏幕按钮 -->
    <div class="add-to-home-trigger" id="addToHomeBtn" style="display: none;">
        <i class="fas fa-download"></i>
        <span class="add-to-home-text">安装</span>
    </div>

    <!-- 登录/注册模态框 -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modalTitle">登录</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="authForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="auth-btn primary" id="authSubmitBtn">登录</button>
                        <button type="button" class="auth-btn secondary" id="authSwitchBtn">没有账号？注册</button>
                    </div>
                </form>
                <div class="auth-message" id="authMessage" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- 安装指导模态框 -->
    <div class="modal-overlay" id="installGuideModal" style="display: none;">
        <div class="modal install-guide-modal">
            <div class="modal-header">
                <h3>📱 App安装指导</h3>
                <button class="modal-close" id="installGuideClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="install-guide-content" id="installGuideContent">
                    <!-- 内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- PWA安装提示 -->
    <div id="installPrompt" class="install-prompt" style="display: none;">
        <div class="install-content">
            <div class="install-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="install-text">
                <h3>安装到手机</h3>
                <p>将英语闪卡添加到主屏幕，获得更好的学习体验</p>
            </div>
            <div class="install-actions">
                <button id="installBtn" class="install-btn primary">
                    <i class="fas fa-download"></i>
                    安装
                </button>
                <button id="dismissBtn" class="install-btn secondary">
                    <i class="fas fa-times"></i>
                    稍后
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- PWA Service Worker 注册 -->
    <script>
        // 注册Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // PWA安装提示
        let deferredPrompt;
        const installPrompt = document.getElementById('installPrompt');
        const installBtn = document.getElementById('installBtn');
        const dismissBtn = document.getElementById('dismissBtn');
        const addToHomeBtn = document.getElementById('addToHomeBtn');

        window.addEventListener('beforeinstallprompt', (e) => {
            // 阻止默认的安装提示
            e.preventDefault();
            deferredPrompt = e;

            // 显示手动安装按钮
            addToHomeBtn.style.display = 'flex';

            // 延迟显示安装提示
            setTimeout(() => {
                installPrompt.style.display = 'block';
                setTimeout(() => {
                    installPrompt.classList.add('show');
                }, 100);
            }, 5000); // 5秒后显示
        });

        installBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                installPrompt.style.display = 'none';
            }
        });

        dismissBtn.addEventListener('click', () => {
            installPrompt.style.display = 'none';
            deferredPrompt = null;
        });

        // 手动安装按钮点击事件
        addToHomeBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                if (outcome === 'accepted') {
                    addToHomeBtn.style.display = 'none';
                }
                deferredPrompt = null;
            } else {
                // 如果没有安装提示，显示指导信息
                showInstallGuide();
            }
        });

        // 显示安装指导
        function showInstallGuide() {
            const modal = document.getElementById('installGuideModal');
            const content = document.getElementById('installGuideContent');

            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
            const isChrome = navigator.userAgent.includes('Chrome');
            const isAndroid = /Android/.test(navigator.userAgent);

            let guideHTML = '';

            if (isIOS && isSafari) {
                guideHTML = `
                    <div class="guide-section">
                        <h4><i class="fab fa-safari"></i>iOS Safari 安装步骤</h4>
                        <ol class="guide-steps">
                            <li>点击底部工具栏的分享按钮 <i class="fas fa-share" style="color: #007AFF;"></i></li>
                            <li>在弹出菜单中找到"添加到主屏幕"</li>
                            <li>点击"添加到主屏幕"</li>
                            <li>确认应用名称，点击"添加"</li>
                            <li>应用图标将出现在主屏幕上</li>
                        </ol>
                    </div>
                `;
            } else if (isChrome && isAndroid) {
                guideHTML = `
                    <div class="guide-section">
                        <h4><i class="fab fa-chrome"></i>Android Chrome 安装步骤</h4>
                        <ol class="guide-steps">
                            <li>点击右上角菜单按钮 <i class="fas fa-ellipsis-v" style="color: #4285F4;"></i></li>
                            <li>选择"安装应用"或"添加到主屏幕"</li>
                            <li>在弹出对话框中点击"安装"</li>
                            <li>应用将自动添加到桌面</li>
                        </ol>
                        <p style="margin-top: 15px; color: #666; font-size: 0.9rem;">
                            💡 提示：如果没有看到"安装应用"选项，请多使用网站一段时间后再试
                        </p>
                    </div>
                `;
            } else {
                guideHTML = `
                    <div class="guide-section">
                        <h4><i class="fas fa-mobile-alt"></i>通用安装方法</h4>
                        <ol class="guide-steps">
                            <li>使用Chrome或Safari浏览器访问本网站</li>
                            <li>在网站上停留并使用一段时间</li>
                            <li>浏览器会自动弹出安装提示</li>
                            <li>点击"安装"或"添加到主屏幕"</li>
                        </ol>
                    </div>

                    <div class="guide-section">
                        <h4><i class="fab fa-chrome"></i>Chrome浏览器</h4>
                        <ol class="guide-steps">
                            <li>点击地址栏右侧的安装图标</li>
                            <li>或者菜单 → "安装应用"</li>
                        </ol>
                    </div>

                    <div class="guide-section">
                        <h4><i class="fab fa-safari"></i>Safari浏览器</h4>
                        <ol class="guide-steps">
                            <li>点击分享按钮 <i class="fas fa-share"></i></li>
                            <li>选择"添加到主屏幕"</li>
                        </ol>
                    </div>
                `;
            }

            content.innerHTML = guideHTML;
            modal.style.display = 'flex';
        }

        // 检测是否已安装为PWA
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            installPrompt.style.display = 'none';
        });

        // 检测是否在PWA模式下运行
        if (window.matchMedia('(display-mode: standalone)').matches) {
            console.log('Running in PWA mode');
            document.body.classList.add('pwa-mode');
        }

        // 离线/在线状态检测
        const offlineIndicator = document.getElementById('offlineIndicator');

        function updateOnlineStatus() {
            if (navigator.onLine) {
                offlineIndicator.classList.remove('show');
            } else {
                offlineIndicator.classList.add('show');
            }
        }

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);

        // 初始检查
        updateOnlineStatus();

        // App下载功能绑定
        const installPWABtn = document.getElementById('installPWABtn');
        const showGuideBtn = document.getElementById('showGuideBtn');
        const installGuideModal = document.getElementById('installGuideModal');
        const installGuideClose = document.getElementById('installGuideClose');

        // PWA安装按钮
        installPWABtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                if (outcome === 'accepted') {
                    // 隐藏下载区域
                    document.getElementById('appDownloadSection').style.display = 'none';
                }
                deferredPrompt = null;
            } else {
                showInstallGuide();
            }
        });

        // 显示安装指导按钮
        showGuideBtn.addEventListener('click', () => {
            showInstallGuide();
        });

        // 关闭安装指导模态框
        installGuideClose.addEventListener('click', () => {
            installGuideModal.style.display = 'none';
        });

        // 点击模态框背景关闭
        installGuideModal.addEventListener('click', (e) => {
            if (e.target === installGuideModal) {
                installGuideModal.style.display = 'none';
            }
        });

        // 检测PWA安装状态
        if (window.matchMedia('(display-mode: standalone)').matches) {
            // 已经以PWA模式运行，隐藏下载区域
            document.getElementById('appDownloadSection').style.display = 'none';
        }

        // 监听PWA安装成功事件
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            document.getElementById('appDownloadSection').style.display = 'none';
        });
    </script>
</body>
</html>
