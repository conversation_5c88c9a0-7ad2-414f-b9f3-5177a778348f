import sqlite3
from werkzeug.security import generate_password_hash
from datetime import datetime, date

print("开始添加测试数据...")

try:
    # 连接数据库
    conn = sqlite3.connect('flashcard.db')
    cursor = conn.cursor()
    print("成功连接到数据库")
    
    # 1. 添加测试用户
    print("\n1. 添加测试用户...")
    test_users = [
        ('testuser1', 'password123'),
        ('testuser2', 'password456'),
        ('demo_user', 'demo123')
    ]
    
    for username, password in test_users:
        # 检查用户是否已存在
        cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
        if not cursor.fetchone():
            password_hash = generate_password_hash(password)
            cursor.execute('''
                INSERT INTO users (username, password_hash, created_at) 
                VALUES (?, ?, ?)
            ''', (username, password_hash, datetime.now()))
            print(f"  添加用户: {username}")
        else:
            print(f"  用户已存在: {username}")
    
    # 2. 获取用户ID
    cursor.execute('SELECT id, username FROM users')
    users = cursor.fetchall()
    print(f"\n当前用户列表:")
    for user_id, username in users:
        print(f"  ID: {user_id}, 用户名: {username}")
    
    # 3. 添加学习进度数据
    print("\n2. 添加学习进度数据...")
    if users:
        user_id = users[0][0]  # 使用第一个用户
        test_words = [
            ('apple', 'learning', 2),
            ('book', 'mastered', 5),
            ('computer', 'review', 3),
            ('dog', 'learning', 1),
            ('elephant', 'mastered', 7),
            ('friend', 'review', 4),
            ('guitar', 'learning', 2),
            ('house', 'mastered', 6),
            ('internet', 'learning', 1),
            ('journey', 'review', 3)
        ]
        
        for word, status, streak in test_words:
            cursor.execute('''
                INSERT OR REPLACE INTO user_progress 
                (user_id, word, status, streak, last_seen) 
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, word, status, streak, datetime.now()))
            print(f"  添加单词进度: {word} ({status}, 连击: {streak})")
    
    # 4. 添加学习记录数据
    print("\n3. 添加学习记录数据...")
    if users:
        user_id = users[0][0]  # 使用第一个用户
        test_sessions = [
            (date(2024, 1, 15), 25, 2),
            (date(2024, 1, 16), 30, 3),
            (date(2024, 1, 17), 20, 1),
            (date(2024, 1, 18), 35, 2),
            (date(2024, 1, 19), 40, 3),
            (date(2024, 1, 20), 28, 2),
            (date(2024, 1, 21), 32, 1)
        ]
        
        for session_date, words_reviewed, sessions_count in test_sessions:
            cursor.execute('''
                INSERT OR REPLACE INTO study_sessions 
                (user_id, session_date, words_reviewed, sessions_count) 
                VALUES (?, ?, ?, ?)
            ''', (user_id, session_date, words_reviewed, sessions_count))
            print(f"  添加学习记录: {session_date} (复习{words_reviewed}个单词, {sessions_count}次会话)")
    
    # 提交更改
    conn.commit()
    print("\n✅ 测试数据添加完成!")
    
    # 5. 验证数据
    print("\n4. 验证添加的数据...")
    
    # 检查用户表
    cursor.execute('SELECT COUNT(*) FROM users')
    user_count = cursor.fetchone()[0]
    print(f"  用户表: {user_count} 条记录")
    
    # 检查学习进度表
    cursor.execute('SELECT COUNT(*) FROM user_progress')
    progress_count = cursor.fetchone()[0]
    print(f"  学习进度表: {progress_count} 条记录")
    
    # 检查学习记录表
    cursor.execute('SELECT COUNT(*) FROM study_sessions')
    session_count = cursor.fetchone()[0]
    print(f"  学习记录表: {session_count} 条记录")
    
    conn.close()
    print("\n🎉 所有测试数据添加成功!")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("脚本执行结束")
