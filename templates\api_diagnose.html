<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-item {
            background: white;
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ddd;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .test-item.success {
            border-left-color: #4CAF50;
        }
        .test-item.error {
            border-left-color: #f44336;
        }
        .test-name {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        .test-status {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        .test-details {
            color: #666;
            font-size: 0.9rem;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .summary {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 ModelScope API 诊断工具</h1>
        <p>检查您的DeepSeek-R1-Distill-Qwen-7B-GGUF模型API连接状态</p>
    </div>

    <div class="summary">
        <button class="btn" onclick="runDiagnosis()">🚀 开始诊断</button>
        <button class="btn" onclick="location.href='/'">🏠 返回主页</button>
    </div>

    <div id="results"></div>

    <script>
        async function runDiagnosis() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">🔄 正在诊断API连接状态...</div>';

            try {
                const response = await fetch('/api/diagnose');
                const data = await response.json();

                let html = '';
                let successCount = 0;
                let totalCount = data.results.length;

                data.results.forEach(result => {
                    const isSuccess = result.status.includes('✅');
                    if (isSuccess) successCount++;

                    html += `
                        <div class="test-item ${isSuccess ? 'success' : 'error'}">
                            <div class="test-name">${result.test}</div>
                            <div class="test-status">${result.status}</div>
                            <div class="test-details">${result.details}</div>
                        </div>
                    `;
                });

                // 添加总结
                const summaryClass = successCount === totalCount ? 'success' : 'error';
                const summaryIcon = successCount === totalCount ? '🎉' : '⚠️';
                const summaryText = successCount === totalCount ? 
                    'API工作正常！' : 
                    `发现问题：${totalCount - successCount}/${totalCount} 项测试失败`;

                html = `
                    <div class="test-item ${summaryClass}">
                        <div class="test-name">${summaryIcon} 诊断总结</div>
                        <div class="test-status">${summaryText}</div>
                        <div class="test-details">成功: ${successCount}/${totalCount}</div>
                    </div>
                ` + html;

                resultsDiv.innerHTML = html;

                // 如果有问题，显示解决建议
                if (successCount < totalCount) {
                    showTroubleshootingTips();
                }

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-item error">
                        <div class="test-name">❌ 诊断失败</div>
                        <div class="test-status">无法连接到诊断API</div>
                        <div class="test-details">错误: ${error.message}</div>
                    </div>
                `;
            }
        }

        function showTroubleshootingTips() {
            const tips = `
                <div class="test-item" style="border-left-color: #ff9800;">
                    <div class="test-name">🔧 故障排除建议</div>
                    <div class="test-details">
                        <h4>常见问题解决方案：</h4>
                        <ul>
                            <li><strong>OpenAI库导入失败</strong>: 运行 <code>pip3.10 install --user openai</code></li>
                            <li><strong>API调用失败</strong>: 
                                <ul>
                                    <li>检查网络连接</li>
                                    <li>验证API密钥是否正确</li>
                                    <li>确认模型名称: unsloth/DeepSeek-R1-Distill-Qwen-7B-GGUF</li>
                                    <li>检查ModelScope服务状态</li>
                                </ul>
                            </li>
                            <li><strong>超时错误</strong>: ModelScope API响应可能较慢，请耐心等待</li>
                            <li><strong>认证错误</strong>: 检查API密钥格式和权限</li>
                        </ul>
                        
                        <h4>联系信息：</h4>
                        <p>如果问题持续存在，请检查：</p>
                        <ul>
                            <li>ModelScope平台服务状态</li>
                            <li>API密钥是否过期</li>
                            <li>网络防火墙设置</li>
                        </ul>
                    </div>
                </div>
            `;
            
            document.getElementById('results').innerHTML += tips;
        }

        // 页面加载时自动运行诊断
        window.onload = function() {
            runDiagnosis();
        };
    </script>
</body>
</html>
